#!/usr/bin/env python3
"""
Quick Test Script for Policybazaar Voice Bot UI
"""

import asyncio
import subprocess
import time
import webbrowser
import sys
import os
from pathlib import Path

def check_voice_bot_running():
    """Check if voice bot is already running"""
    try:
        result = subprocess.run(['pgrep', '-f', 'voice_bot.py'], capture_output=True, text=True)
        return bool(result.stdout.strip())
    except:
        return False

def start_voice_bot():
    """Start the voice bot in background"""
    try:
        print("🚀 Starting voice bot...")
        # Start voice bot in background
        process = subprocess.Popen([
            sys.executable, 'voice_bot.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for it to start
        time.sleep(3)
        
        # Check if it's running
        if process.poll() is None:
            print("✅ Voice bot started successfully!")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ Voice bot failed to start:")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"❌ Error starting voice bot: {e}")
        return None

def open_test_ui():
    """Open the test UI in browser"""
    try:
        # Get current directory
        current_dir = Path.cwd()
        ui_file = current_dir / "test_ui.html"
        
        if ui_file.exists():
            # Open in browser
            file_url = f"file://{ui_file.absolute()}"
            print(f"🌐 Opening test UI: {file_url}")
            webbrowser.open(file_url)
            return True
        else:
            print(f"❌ Test UI file not found: {ui_file}")
            return False
            
    except Exception as e:
        print(f"❌ Error opening test UI: {e}")
        return False

def main():
    print("🎤 Policybazaar Voice Bot - Quick Test")
    print("=" * 50)
    
    # Check if already running
    if check_voice_bot_running():
        print("ℹ️ Voice bot is already running")
    else:
        # Start voice bot
        process = start_voice_bot()
        if not process:
            print("❌ Failed to start voice bot. Please check logs.")
            return
    
    # Open test UI
    if open_test_ui():
        print("\n✅ Test environment ready!")
        print("\n📋 Testing Instructions:")
        print("1. Click 'Simulate Dialer Connection' to start")
        print("2. Use 'Yes/No/Unclear' buttons for quick testing")
        print("3. Or use 'Start Speaking' for microphone input")
        print("4. Watch the conversation log for responses")
        print("\n🔍 Monitor logs with: tail -f logs/voice_bot.log")
        print("🛑 To stop voice bot: pkill -f voice_bot.py")
        
        # Keep script running
        try:
            print("\n⏳ Press Ctrl+C to exit...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 Stopping test environment...")
            subprocess.run(['pkill', '-f', 'voice_bot.py'], capture_output=True)
            print("✅ Voice bot stopped")
    else:
        print("❌ Failed to open test UI")

if __name__ == "__main__":
    main() 