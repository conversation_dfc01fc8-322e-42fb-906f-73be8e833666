#!/bin/bash

# Policybazaar Voice Bot - Production Startup Script
# Usage: ./start_production.sh

set -e

echo "🚀 Starting Policybazaar Voice Bot in Production Mode..."

# Check if Python 3.8+ is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Python version $PYTHON_VERSION is too old. Please install Python 3.8 or higher."
    exit 1
fi

echo "✅ Python version: $PYTHON_VERSION"

# Check if virtual environment exists, create if not
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install/upgrade dependencies
echo "📦 Installing/upgrading dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Check if Google Cloud credentials exist
if [ ! -f "matrixteam.json" ]; then
    echo "❌ Google Cloud credentials file 'matrixteam.json' not found!"
    echo "Please ensure the service account key file is present."
    exit 1
fi

# Set environment variables
export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/matrixteam.json"
export PYTHONPATH="$(pwd)"

# Create logs directory if it doesn't exist
mkdir -p logs

# Check if voice bot is already running
if pgrep -f "voice_bot.py" > /dev/null; then
    echo "⚠️ Voice bot is already running. Stopping existing process..."
    pkill -f "voice_bot.py"
    sleep 2
fi

# Start the voice bot
echo "🎤 Starting Policybazaar Voice Bot..."
echo "📍 Server will be available at: ws://0.0.0.0:8765"
echo "📊 Logs will be written to: logs/voice_bot.log"
echo "⏰ Started at: $(date)"

# Run the voice bot with proper error handling
python3 voice_bot.py 2>&1 | tee -a logs/startup.log

echo "✅ Voice bot started successfully!"
echo "🔍 To monitor logs: tail -f logs/voice_bot.log"
echo "🛑 To stop: pkill -f voice_bot.py" 