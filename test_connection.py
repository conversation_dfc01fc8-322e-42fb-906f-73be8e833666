#!/usr/bin/env python3
"""
Simple test script to verify WebSocket connection and greeting functionality
"""

import asyncio
import websockets
import json
import sys

async def test_connection():
    """Test the WebSocket connection and greeting"""
    try:
        print("🔌 Testing WebSocket connection...")
        
        # Connect to the voice bot
        uri = "ws://localhost:8765/ws"
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to voice bot")
            
            # Send init signal to trigger greeting
            init_message = {
                "type": "signal",
                "data": "__INIT__"
            }
            await websocket.send(json.dumps(init_message))
            print("📤 Sent init signal")
            
            # Wait for greeting response
            print("⏳ Waiting for greeting...")
            response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
            
            data = json.loads(response)
            print(f"📥 Received response: {data.get('type', 'unknown')}")
            print(f"🔍 Full response data: {data}")
            
            if data.get('type') == 'audio':
                print("✅ Greeting received successfully!")
                print(f"📝 Text: {data.get('text', 'No text')}")
                print(f"🎵 Audio length: {len(data.get('audio_b64', ''))} chars")
                return True
            else:
                print(f"❌ Unexpected response type: {data}")
                return False
                
    except asyncio.TimeoutError:
        print("❌ Timeout waiting for greeting")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

async def test_text_response():
    """Test text response functionality"""
    try:
        print("\n📝 Testing text response...")
        
        uri = "ws://localhost:8765/ws"
        async with websockets.connect(uri) as websocket:
            # Send init signal
            await websocket.send(json.dumps({"type": "signal", "data": "__INIT__"}))
            await websocket.recv()  # Wait for greeting
            
            # Send text response
            text_message = {
                "type": "text",
                "data": "Yes, I am interested"
            }
            await websocket.send(json.dumps(text_message))
            print("📤 Sent 'Yes' response")
            
            # Wait for AI response
            response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
            data = json.loads(response)
            
            if data.get('type') == 'audio':
                print("✅ Text response received!")
                print(f"📝 AI Response: {data.get('text', 'No text')}")
                print(f"🎯 Intent: {data.get('customer_intent', 'Unknown')}")
                print(f"⚡ Next Action: {data.get('next_action', 'Unknown')}")
                return True
            else:
                print(f"❌ Unexpected response: {data}")
                return False
                
    except Exception as e:
        print(f"❌ Error in text test: {e}")
        return False

async def main():
    """Run all tests"""
    print("🎤 Policybazaar Voice Bot - Connection Test")
    print("=" * 50)
    
    # Test 1: Basic connection and greeting
    greeting_success = await test_connection()
    
    # Test 2: Text response
    text_success = await test_text_response()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"✅ Greeting Test: {'PASS' if greeting_success else 'FAIL'}")
    print(f"✅ Text Response Test: {'PASS' if text_success else 'FAIL'}")
    
    if greeting_success and text_success:
        print("\n🎉 All tests passed! UI should work correctly.")
    else:
        print("\n❌ Some tests failed. Check voice bot logs.")

if __name__ == "__main__":
    asyncio.run(main()) 