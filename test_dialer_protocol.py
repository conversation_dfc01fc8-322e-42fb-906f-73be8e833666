#!/usr/bin/env python3
"""
Test script to simulate dialer.py protocol and test voice bot compatibility
"""

import asyncio
import json
import base64
import websockets
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_dialer_protocol():
    """Test the dialer protocol with our voice bot"""
    
    # Connect to voice bot
    uri = "ws://localhost:8765/ws"
    
    try:
        async with websockets.connect(uri) as websocket:
            logger.info("🔌 Connected to voice bot")
            
            # 1. Send start event (like dialer.py does)
            start_event = {
                "event": "start",
                "sequence_number": 1,
                "stream_id": "test_call_123",
                "bytes": None,
                "start": {
                    "stream_sid": "test_call_123",
                    "user_name": "Test User",
                    "flow_name": "test_flow",
                    "call_sid": "test_call_123",
                    "account_sid": "test_account",
                    "voice_name": "test_voice"
                }
            }
            
            logger.info("📤 Sending start event...")
            await websocket.send(json.dumps(start_event))
            
            # 2. Wait for greeting response
            logger.info("⏳ Waiting for greeting response...")
            response = await websocket.recv()
            data = json.loads(response)
            logger.info(f"📥 Received response: {data.get('type')}")
            
            if data.get('type') == 'audio':
                logger.info("✅ Greeting received successfully!")
                logger.info(f"📝 Text: {data.get('text', 'No text')}")
                logger.info(f"🎵 Audio size: {len(data.get('data', ''))} chars")
            else:
                logger.warning(f"⚠️ Unexpected response type: {data.get('type')}")
            
            # 3. Send a test audio chunk (simulating customer response)
            logger.info("📤 Sending test audio chunk...")
            
            # Create a simple test audio chunk (silence)
            test_audio = b'\x00' * 320  # 320 bytes of silence (like dialer sends)
            audio_message = {
                'type': 'audio',
                'data': base64.b64encode(test_audio).decode('utf-8')
            }
            
            await websocket.send(json.dumps(audio_message))
            
            # 4. Wait for response
            logger.info("⏳ Waiting for response to audio...")
            response = await websocket.recv()
            data = json.loads(response)
            logger.info(f"📥 Received response: {data.get('type')}")
            
            if data.get('type') == 'audio':
                logger.info("✅ Audio response received!")
                logger.info(f"📝 Text: {data.get('text', 'No text')}")
            else:
                logger.warning(f"⚠️ Unexpected response type: {data.get('type')}")
            
            # 5. Send text response (simulating customer saying "Yes")
            logger.info("📤 Sending text response: 'Yes'...")
            text_message = {
                'type': 'text',
                'data': 'Yes'
            }
            
            await websocket.send(json.dumps(text_message))
            
            # 6. Wait for final response
            logger.info("⏳ Waiting for final response...")
            response = await websocket.recv()
            data = json.loads(response)
            logger.info(f"📥 Received response: {data.get('type')}")
            
            if data.get('type') == 'audio':
                logger.info("✅ Final response received!")
                logger.info(f"📝 Text: {data.get('text', 'No text')}")
                logger.info(f"🎯 Next action: {data.get('next_action', 'unknown')}")
            else:
                logger.warning(f"⚠️ Unexpected response type: {data.get('type')}")
            
            logger.info("✅ Dialer protocol test completed successfully!")
            
    except Exception as e:
        logger.error(f"❌ Error in dialer protocol test: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    logger.info("🧪 Starting dialer protocol test...")
    asyncio.run(test_dialer_protocol()) 