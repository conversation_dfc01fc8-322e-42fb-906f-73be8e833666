"""
Configuration file for PolicyBazaar Voice Bot
"""

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT = "named-defender-182511"
SERVICE_ACCOUNT_FILE = "matrixteam.json"

# Audio Configuration
AUDIO_CONFIG = {
    "sample_rate": 16000,
    "channels": 1,
    "encoding": "LINEAR16",  # 16-bit PCM
    "chunk_duration_ms": 100,  # Process 100ms chunks for low latency
}

# Speech Recognition Configuration
SPEECH_CONFIG = {
    "language_code": "en-IN",  # Indian English
    "enable_automatic_punctuation": True,
    "enable_word_time_offsets": False,
    "model": "latest_long",
    "use_enhanced": True,
}

# Text-to-Speech Configuration
TTS_CONFIG = {
    "language_code": "en-IN",
    "voice_name": "en-IN-Standard-A",
    "ssml_gender": "FEMALE",
    "audio_encoding": "LINEAR16",
    "sample_rate_hertz": 8000,  # Reduced from 16000 to make audio smaller
}

# AI Configuration
AI_CONFIG = {
    "model": "gemini-2.5-flash",
    "temperature": 0.7,
    "max_output_tokens": 1024,  # Increased from 150 to fix MAX_TOKENS error
    "top_p": 0.8,
    "context_messages": 5,  # Keep last 5 messages for context
    "max_history": 10,  # Keep last 10 messages in session
}

# System Prompt for Policybazaar
SYSTEM_PROMPT = """You are a professional Policybazaar customer service representative. 
You help customers with insurance queries, policy information, and general assistance.
Be polite, professional, and provide accurate information about insurance products.
Keep responses concise and helpful. Focus on:
- Health insurance queries and plans
- Motor insurance (car/bike) policies
- Life insurance products
- Travel insurance options
- Policy renewals and procedures
- Claims assistance and process
- Premium calculations and quotes
- Policy comparisons and recommendations

Always ask for relevant details when needed and provide clear, actionable information.
Keep responses under 200 words and be conversational."""

# Outbound Call System Prompt for Policybazaar
OUTBOUND_CALL_PROMPT = """You are a professional voice assistant representing Policybazaar, India's leading insurance platform. Your job is to interact with customers over phone calls in a friendly, polite, and professional tone. Keep responses short and direct, suitable for real-time telephonic conversation. Always identify the customer's intent clearly and avoid vague or open-ended answers. If the customer sounds interested, offer to connect them with a certified insurance advisor. If the customer is uninterested or says 'no', end the call politely."""

# Dialer Integration Configuration
DIALER_CONFIG = {
    "audio_format": {
        "sample_rate": 8000,  # 8kHz as per dialer team's specification
        "channels": 1,        # Mono
        "sample_width": 2,    # 16-bit
        "chunk_size": 320,    # 320 bytes = 20ms of audio at 8kHz 16-bit mono
        "encoding": "LINEAR16"
    },
    "websocket": {
        "endpoint": "/ws",    # Endpoint that dialer team will connect to
        "ping_interval": 20,
        "ping_timeout": 10,
        "close_timeout": 10
    },
    "audio_processing": {
        "buffer_chunks": 10,  # Process audio every 10 chunks
        "max_silence_duration": 5.0,  # Maximum silence before timeout
        "speech_timeout": 30.0        # Maximum time to wait for speech
    },
    "signals": {
        "timeout": "__TIMEOUT__",
        "session_end": "__SESSION_END__", 
        "interrupt": "__INTERRUPT__"
    }
}

# Server Configuration
SERVER_CONFIG = {
    "websocket": {
        "host": "0.0.0.0",  # Changed from localhost for production
        "port": 8765,
        "ping_interval": 20,
        "ping_timeout": 20,
    },
    "rest_api": {
        "host": "0.0.0.0",
        "port": 8000,
    }
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": "logs/voice_bot.log",  # Added file logging for production
    "max_bytes": 10485760,  # 10MB
    "backup_count": 5,
}

# Performance Configuration
PERFORMANCE_CONFIG = {
    "max_concurrent_sessions": 100,
    "session_timeout_seconds": 300,  # 5 minutes
    "audio_buffer_size": 8192,
    "enable_caching": True,
    "cache_ttl_seconds": 3600,  # 1 hour
    "max_connections": 200,  # Added max connections for production
    "connection_timeout": 30,  # Added connection timeout
}

# Error Messages
ERROR_MESSAGES = {
    "audio_processing": "I apologize, but I'm having trouble processing your audio. Please try again.",
    "ai_generation": "I apologize, but I'm having trouble generating a response. Please try again.",
    "tts_error": "I apologize, but I'm having trouble converting my response to speech. Please try again.",
    "network_error": "I apologize, but there's a network issue. Please try again.",
    "session_expired": "Your session has expired. Please start a new conversation.",
} 