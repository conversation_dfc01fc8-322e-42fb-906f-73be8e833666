#!/usr/bin/env python3
"""
PolicyBazaar Voice Bot - Professional Outbound Call System

Phase 1: Simple intent detection (YES/NO) with hardcoded responses
Phase 2: Full AI conversations (future implementation)

Supports:
- Dialer integration via WebSocket
- UI testing interface
- Multi-language intent detection
- Professional call flow management
"""

import asyncio
import base64
import json
import logging
import os
import re
import sys
import time
import traceback
from typing import Optional, Dict, Any
from datetime import datetime

import websockets
from websockets.server import WebSocketServerProtocol
from google.cloud import speech_v1, texttospeech
from google.cloud import aiplatform
from vertexai.generative_models import GenerativeModel
from pydub import AudioSegment
import io
import numpy as np
from collections import deque

# Import configuration
from config import (
    GOOGLE_CLOUD_PROJECT, SERVICE_ACCOUNT_FILE,
    AUDIO_CONFIG, SPEECH_CONFIG, TTS_CONFIG, AI_CONFIG,
    SYSTEM_PROMPT, OUTBOUND_CALL_PROMPT, DIALER_CONFIG,
    SERVER_CONFIG, LOGGING_CONFIG, PERFORMANCE_CONFIG,
    ERROR_MESSAGES
)

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG["level"]),
    format=LOGGING_CONFIG["format"],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG["file"]),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class VoiceActivityDetector:
    """
    Professional Voice Activity Detection for real-time audio processing
    
    Features:
    - Real-time voice activity detection
    - Automatic silence detection
    - Configurable thresholds
    - Production-ready performance
    """
    
    def __init__(self,
                 silence_threshold_ms: int = 800,   # Reduced for faster response
                 min_speech_ms: int = 300,          # Reduced for quicker detection
                 energy_threshold: float = 0.008,   # Lower threshold for better sensitivity
                 buffer_size: int = 1600):          # 100ms buffer at 16kHz
        self.silence_threshold_ms = silence_threshold_ms
        self.min_speech_ms = min_speech_ms
        self.energy_threshold = energy_threshold
        self.buffer_size = buffer_size
        
        # Audio buffers for real-time processing
        self.audio_buffers = {}
        self.speech_start_times = {}
        self.last_voice_times = {}
        self.is_speaking = {}
        
        logger.info(f"🎤 Voice Activity Detector initialized - Silence threshold: {silence_threshold_ms}ms")
    
    def _calculate_energy(self, audio_chunk: bytes) -> float:
        """Calculate energy level of audio chunk"""
        try:
            # Convert bytes to numpy array
            audio_array = np.frombuffer(audio_chunk, dtype=np.int16)
            # Calculate RMS energy
            energy = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
            return energy
        except Exception as e:
            logger.error(f"❌ Error calculating energy: {e}")
            return 0.0
    
    def _is_voice_activity(self, audio_chunk: bytes) -> bool:
        """Detect if audio chunk contains voice activity"""
        energy = self._calculate_energy(audio_chunk)
        return energy > self.energy_threshold
    
    def process_audio_chunk(self, session_id: str, audio_chunk: bytes, timestamp_ms: int) -> Dict[str, Any]:
        """
        Process audio chunk and detect voice activity
        
        Returns:
        - 'is_speaking': bool - Whether user is currently speaking
        - 'should_process': bool - Whether to process the audio
        - 'speech_duration': int - Duration of current speech in ms
        """
        try:
            # Initialize session buffers
            if session_id not in self.audio_buffers:
                self.audio_buffers[session_id] = deque(maxlen=150)  # Increased buffer for better quality
                self.speech_start_times[session_id] = None
                self.last_voice_times[session_id] = timestamp_ms
                self.is_speaking[session_id] = False
            
            # Add chunk to buffer
            self.audio_buffers[session_id].append(audio_chunk)
            
            # Detect voice activity
            has_voice = self._is_voice_activity(audio_chunk)
            
            if has_voice:
                # Voice detected
                self.last_voice_times[session_id] = timestamp_ms
                
                if not self.is_speaking[session_id]:
                    # Speech started
                    self.speech_start_times[session_id] = timestamp_ms
                    self.is_speaking[session_id] = True
                    logger.info(f"🎤 Speech started for session {session_id}")
                
                return {
                    'is_speaking': True,
                    'should_process': False,  # Continue collecting
                    'speech_duration': timestamp_ms - self.speech_start_times[session_id]
                }
            else:
                # No voice detected
                if self.is_speaking[session_id]:
                    # Check if silence threshold exceeded
                    silence_duration = timestamp_ms - self.last_voice_times[session_id]
                    
                    if silence_duration >= self.silence_threshold_ms:
                        # Speech ended
                        speech_duration = self.last_voice_times[session_id] - self.speech_start_times[session_id]
                        
                        if speech_duration >= self.min_speech_ms:
                            # Valid speech detected, process it
                            self.is_speaking[session_id] = False
                            logger.info(f"🎤 Speech ended for session {session_id} - Duration: {speech_duration}ms")
                            
                            return {
                                'is_speaking': False,
                                'should_process': True,
                                'speech_duration': speech_duration
                            }
                        else:
                            # Speech too short, ignore
                            self.is_speaking[session_id] = False
                            logger.info(f"🎤 Speech too short for session {session_id} - Duration: {speech_duration}ms")
                
                return {
                    'is_speaking': False,
                    'should_process': False,
                    'speech_duration': 0
                }
                
        except Exception as e:
            logger.error(f"❌ Error in voice activity detection: {e}")
            return {
                'is_speaking': False,
                'should_process': False,
                'speech_duration': 0
            }
    
    def get_audio_buffer(self, session_id: str) -> bytes:
        """Get accumulated audio buffer for processing"""
        if session_id in self.audio_buffers:
            return b''.join(self.audio_buffers[session_id])
        return b''
    
    def clear_buffer(self, session_id: str):
        """Clear audio buffer after processing"""
        if session_id in self.audio_buffers:
            self.audio_buffers[session_id].clear()
            self.speech_start_times[session_id] = None
            self.is_speaking[session_id] = False


class PolicyBazaarVoiceBot:
    """
    Professional Voice Bot for PolicyBazaar Outbound Calls
    
    Features:
    - Phase 1: Simple intent detection (YES/NO) with hardcoded responses
    - Phase 2: Full AI conversations (future implementation)
    - Multi-language support
    - Dialer integration
    - UI testing interface
    """
    
    def __init__(self):
        """Initialize the voice bot with all required services"""
        try:
            logger.info("🚀 Initializing PolicyBazaar Voice Bot...")
            
            # Initialize Google Cloud services
            self._initialize_google_services()
            
            # Initialize conversation tracking
            self.conversation_context = {}
            self.active_connections = 0
            self.total_connections = 0
            
            # Initialize Voice Activity Detector for real-time processing
            # Optimized for professional conversation flow
            self.vad = VoiceActivityDetector(
                silence_threshold_ms=800,   # Reduced to 800ms for faster response
                min_speech_ms=300,         # Reduced to 300ms for quicker detection
                energy_threshold=0.008     # Slightly lower threshold for better sensitivity
            )
            
            # Phase 1: Hardcoded responses for simple intent detection
            self._initialize_phase1_responses()
            
            logger.info("✅ PolicyBazaar Voice Bot initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize voice bot: {e}")
            raise
    
    def _initialize_google_services(self):
        """Initialize Google Cloud services (Speech, TTS, Vertex AI)"""
        try:
            # Set Google Cloud credentials
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = SERVICE_ACCOUNT_FILE
            
            # Initialize Speech-to-Text client
            self.speech_client = speech_v1.SpeechClient()
            self.speech_config = speech_v1.RecognitionConfig(
                language_code=SPEECH_CONFIG["language_code"],
                enable_automatic_punctuation=SPEECH_CONFIG["enable_automatic_punctuation"],
                enable_word_time_offsets=SPEECH_CONFIG["enable_word_time_offsets"],
                model=SPEECH_CONFIG["model"],
                use_enhanced=SPEECH_CONFIG["use_enhanced"]
            )
            logger.info("✅ Speech recognition config created")
            
            # Initialize Text-to-Speech client
            self.tts_client = texttospeech.TextToSpeechClient()
            self.tts_config = texttospeech.VoiceSelectionParams(
                language_code=TTS_CONFIG["language_code"],
                name=TTS_CONFIG["voice_name"],
                ssml_gender=TTS_CONFIG["ssml_gender"]
            )
            self.audio_config = texttospeech.AudioConfig(
                audio_encoding=TTS_CONFIG["audio_encoding"],
                sample_rate_hertz=TTS_CONFIG["sample_rate_hertz"]
            )
            logger.info("✅ TTS config created")
            
            # Initialize Vertex AI
            aiplatform.init(project=GOOGLE_CLOUD_PROJECT)
            logger.info("✅ Vertex AI initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Google services: {e}")
            raise

    def _initialize_phase1_responses(self):
        """Initialize Phase 1 hardcoded responses for simple intent detection"""
        self.phase1_responses = {
            "greeting": {
                "message": "Hello! This is Policybazaar, India's leading insurance marketplace. We noticed you recently showed interest in our insurance services. Would you like to speak to an insurance advisor for assistance?",
                "action": "continue",
                "reason": "greeting"
            },
            "interested": {
                "message": "Great! Connecting you to a certified insurance advisor who can assist you further. Please hold the line.",
                "action": "end_call",
                "reason": "connecting_to_agent"
            },
            "not_interested": {
                "message": "No problem, we will send you a link on WhatsApp where you can explore our insurance options. When you open the link, there will be a call button available for you to reach us anytime. Thank you for your time, have a great day!",
                "action": "end_call",
                "reason": "customer_not_interested"
            },
            "unclear": {
                "message": "Sorry, I didn't catch that clearly. Are you looking for assistance with your insurance needs today?",
                "action": "continue",
                "reason": "unclear_response"
            }
        }
        logger.info("✅ Phase 1 responses initialized")

    # ============================================================================
    # PHASE 1: SIMPLE INTENT DETECTION (CURRENT IMPLEMENTATION)
    # ============================================================================
    
    async def analyze_customer_response(self, transcript: str) -> dict:
        """
        Phase 1: Analyze customer response using Vertex AI for simple intent detection
        
        Returns: INTERESTED, NOT_INTERESTED, or UNCLEAR
        """
        try:
            logger.info(f"🤖 Analyzing customer response using Vertex AI: '{transcript}'")
            
            # Use Vertex AI to analyze customer intent
            model = GenerativeModel("gemini-1.5-flash")
            
            # Create a very concise prompt for intent analysis
            intent_prompt = f"Classify: '{transcript}' as INTERESTED, NOT_INTERESTED, or UNCLEAR"

            # Get AI analysis
            response = model.generate_content(
                intent_prompt,
                generation_config={
                    "temperature": 0.0,  # Zero temperature for consistent classification
                    "max_output_tokens": 5,  # Very short response
                    "top_p": 0.1
                }
            )
            
            ai_intent = response.text.strip().upper()
            logger.info(f"🤖 Vertex AI intent analysis: '{ai_intent}' for '{transcript}'")
            
            # Return appropriate Phase 1 response based on AI analysis
            if ai_intent == "INTERESTED":
                logger.info(f"✅ Customer interested (AI): '{transcript}'")
                return self.phase1_responses["interested"]
                
            elif ai_intent == "NOT_INTERESTED":
                logger.info(f"❌ Customer not interested (AI): '{transcript}'")
                return self.phase1_responses["not_interested"]
                
            else:  # UNCLEAR or any other response
                logger.info(f"❓ Unclear response (AI): '{transcript}'")
                return self.phase1_responses["unclear"]
                
        except Exception as e:
            logger.error(f"❌ Error analyzing customer response with Vertex AI: {e}")
            # Fallback to basic keyword matching if AI fails
            normalized_input = transcript.lower().strip()
            if any(phrase in normalized_input for phrase in ["no", "not interested", "not now", "don't call", "wrong number"]):
                logger.info(f"❌ Customer not interested (fallback): '{transcript}'")
                return self.phase1_responses["not_interested"]
            else:
                logger.info(f"✅ Customer interested (fallback): '{transcript}'")
                return self.phase1_responses["interested"]

    # ============================================================================
    # PHASE 2: FULL AI CONVERSATIONS (FUTURE IMPLEMENTATION)
    # ============================================================================
    
    async def generate_ai_response(self, user_input: str, session_id: str) -> Optional[str]:
        """
        Phase 2: Generate AI response using Vertex AI for full conversations
        
        This function is preserved for Phase 2 implementation
        """
        try:
            logger.info(f"🤖 Generating AI response for: '{user_input}'")
            
            # Get conversation history
            history = self.conversation_context.get(session_id, [])
            logger.info(f"📚 Conversation history: {len(history)} messages")
            
            try:
                # Initialize the model
                model = GenerativeModel(AI_CONFIG["model"])
                
                # Build conversation context
                messages = []
                
                # Add system prompt and user input
                prompt = f"{SYSTEM_PROMPT}\n\nUser: {user_input}"
                
                # Add conversation history if available
                if history:
                    # Add last few messages for context (respecting max_history)
                    recent_history = history[-AI_CONFIG["context_messages"]:]
                    context = "\n".join([f"{msg['role']}: {msg['content']}" for msg in recent_history])
                    prompt = f"{SYSTEM_PROMPT}\n\nConversation History:\n{context}\n\nUser: {user_input}"
                
                # Generate response using Vertex AI
                logger.info("🤖 Calling Vertex AI Gemini model...")
                response = model.generate_content(
                    prompt,
                    generation_config={
                        "temperature": AI_CONFIG["temperature"],
                        "max_output_tokens": AI_CONFIG["max_output_tokens"],
                        "top_p": AI_CONFIG["top_p"]
                    }
                )
                
                ai_response = response.text
                
                # Clean the response text for TTS (remove markdown formatting)
                ai_response = self._clean_ai_response(ai_response)
                
                logger.info(f"✅ AI Response from Vertex AI: '{ai_response}'")
                
            except Exception as ai_error:
                logger.error(f"❌ Vertex AI error: {ai_error}")
                # Fallback to a more intelligent response based on the input
                ai_response = self._generate_fallback_response(user_input)
                logger.info(f"✅ Fallback AI Response: '{ai_response}'")
            
            # Update conversation history
            history.append({"role": "user", "content": user_input})
            history.append({"role": "assistant", "content": ai_response})
            self.conversation_context[session_id] = history[-AI_CONFIG["max_history"]:]
            
            return ai_response
            
        except Exception as e:
            logger.error(f"❌ Error generating AI response: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return ERROR_MESSAGES["ai_generation"]

    def _clean_ai_response(self, ai_response: str) -> str:
        """Clean AI response for TTS (remove markdown formatting)"""
        # Remove markdown bold formatting (**text**)
        ai_response = re.sub(r'\*\*(.*?)\*\*', r'\1', ai_response)
        # Remove markdown italic formatting (*text*)
        ai_response = re.sub(r'\*(.*?)\*', r'\1', ai_response)
        # Remove any remaining markdown symbols
        ai_response = re.sub(r'#+\s*', '', ai_response)  # Remove headers
        ai_response = re.sub(r'`(.*?)`', r'\1', ai_response)  # Remove code blocks
        ai_response = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', ai_response)  # Remove links
        # Clean up extra whitespace
        ai_response = re.sub(r'\n\s*\n', '\n', ai_response)  # Remove extra line breaks
        return ai_response.strip()

    def _generate_fallback_response(self, user_input: str) -> str:
        """Generate fallback response based on user input"""
        if "health insurance" in user_input.lower():
            return "I understand you're looking for health insurance. I can help you find the best health insurance plans based on your needs. Could you tell me more about your requirements, such as your age, family size, and any specific medical conditions?"
        elif "car insurance" in user_input.lower() or "motor insurance" in user_input.lower():
            return "I can help you with motor insurance for your car or bike. Please let me know the vehicle details, your driving history, and any specific coverage requirements you have."
        elif "life insurance" in user_input.lower():
            return "I can assist you with life insurance options. To provide the best recommendations, I need to know your age, income, family responsibilities, and any existing insurance coverage."
        elif "travel insurance" in user_input.lower():
            return "I can help you find suitable travel insurance. Please tell me about your travel destination, duration, and any specific activities you'll be doing during your trip."
        else:
            return f"Thank you for your message: '{user_input}'. I'm here to help you with all your insurance needs including health, motor, life, and travel insurance. How can I assist you today?"

    # ============================================================================
    # AUDIO PROCESSING FUNCTIONS
    # ============================================================================
    
    async def text_to_speech(self, text: str) -> bytes:
        """Convert text to speech using Google TTS"""
        try:
            logger.info(f"🔊 Converting to speech: '{text}'")
            
            synthesis_input = texttospeech.SynthesisInput(text=text)
            
            response = self.tts_client.synthesize_speech(
                input=synthesis_input,
                voice=self.tts_config,
                audio_config=self.audio_config
            )
            
            logger.info(f"✅ TTS completed (size: {len(response.audio_content)} bytes)")
            return response.audio_content
            
        except Exception as e:
            logger.error(f"❌ Error in text-to-speech: {e}")
            return None

    async def convert_audio_for_speech_recognition(self, audio_data: bytes) -> bytes:
        """Convert audio to format suitable for Google Speech Recognition"""
        try:
            # Create AudioSegment from raw audio data
            audio_segment = AudioSegment(
                data=audio_data,
                sample_width=2,  # 16-bit = 2 bytes
                frame_rate=16000,  # 16kHz
                channels=1  # Mono
            )
            
            # Export as WAV format for Google Speech Recognition
            wav_buffer = io.BytesIO()
            audio_segment.export(wav_buffer, format="wav")
            wav_data = wav_buffer.getvalue()
            
            logger.info(f"✅ Converted audio for speech recognition: {len(audio_data)} -> {len(wav_data)} bytes")
            return wav_data
            
        except Exception as e:
            logger.error(f"❌ Error converting audio for speech recognition: {e}")
            return audio_data

    async def convert_dialer_audio_format(self, audio_data: bytes) -> bytes:
        """Convert audio from dialer format (8kHz) to our processing format (16kHz)"""
        try:
            # Create AudioSegment from raw audio data
            # Dialer sends 8kHz, 16-bit PCM, mono
            audio_segment = AudioSegment(
                data=audio_data,
                sample_width=2,  # 16-bit = 2 bytes
                frame_rate=8000,  # 8kHz
                channels=1  # Mono
            )
            
            # Convert to 16kHz for Google Speech Recognition
            audio_segment = audio_segment.set_frame_rate(16000)
            
            # Export as WAV format
            wav_buffer = io.BytesIO()
            audio_segment.export(wav_buffer, format="wav")
            wav_data = wav_buffer.getvalue()
            
            logger.info(f"✅ Converted dialer audio: {len(audio_data)} -> {len(wav_data)} bytes")
            return wav_data
            
        except Exception as e:
            logger.error(f"❌ Error converting dialer audio format: {e}")
            return audio_data

    async def convert_ui_audio_format(self, audio_data: bytes, source_format: str = "webm") -> bytes:
        """Convert UI audio format (WebM) to our processing format"""
        try:
            # Create AudioSegment from WebM audio data
            audio_segment = AudioSegment.from_file(io.BytesIO(audio_data), format=source_format)
            
            # Convert to 16kHz mono for Google Speech Recognition
            audio_segment = audio_segment.set_frame_rate(16000)
            audio_segment = audio_segment.set_channels(1)
            audio_segment = audio_segment.set_sample_width(2)  # Force 16-bit
            
            # Export as WAV format with 16-bit samples
            wav_buffer = io.BytesIO()
            audio_segment.export(wav_buffer, format="wav")
            wav_data = wav_buffer.getvalue()
            
            logger.info(f"✅ Converted UI audio: {len(audio_data)} -> {len(wav_data)} bytes")
            return wav_data
            
        except Exception as e:
            logger.error(f"❌ Error converting UI audio format: {e}")
            # For UI testing, return original audio if conversion fails
            # In production, dialer will send proper 16-bit PCM audio
            return audio_data

    # ============================================================================
    # DIALER INTEGRATION FUNCTIONS
    # ============================================================================
    
    async def process_dialer_audio(self, audio_data: bytes, session_id: str) -> dict:
        """Process audio from dialer and generate appropriate response"""
        try:
            logger.info(f"🎤 Processing dialer audio for {session_id} ({len(audio_data)} bytes)")
            
            # Create recognition audio object
            audio = speech_v1.RecognitionAudio(content=audio_data)
            
            # Perform speech recognition
            response = self.speech_client.recognize(
                config=self.speech_config,
                audio=audio
            )
            
            if not response.results:
                logger.warning(f"⚠️ No speech detected in dialer audio for {session_id}")
                return None
            
            # Get transcribed text
            transcript = response.results[0].alternatives[0].transcript
            confidence = response.results[0].alternatives[0].confidence
            logger.info(f"📝 Dialer transcription: '{transcript}' (confidence: {confidence:.2f})")
            
            # Phase 1: Analyze customer response for simple intent detection
            response_text = await self.analyze_customer_response(transcript)
            
            if response_text:
                # Convert to speech
                response_audio = await self.text_to_speech(response_text['message'])
                
                if response_audio:
                    return {
                        'audio_b64': base64.b64encode(response_audio).decode('utf-8'),
                        'transcribed_text': transcript,
                        'ai_response': response_text['message'],
                        'action': response_text.get('action'),
                        'reason': response_text.get('reason')
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error processing dialer audio: {e}")
            return None

    async def send_policybazaar_greeting(self, websocket: WebSocketServerProtocol, session_id: str):
        """Send the Policybazaar greeting when customer answers the call"""
        try:
            logger.info(f"📞 Sending Policybazaar greeting for session {session_id}")
            
            # Get greeting from Phase 1 responses
            greeting_response = self.phase1_responses["greeting"]
            greeting_text = greeting_response['message']
            
            # Convert to speech
            response_audio = await self.text_to_speech(greeting_text)
            
            if response_audio:
                # Send greeting to dialer
                greeting_message = {
                    'type': 'audio',
                    'data': base64.b64encode(response_audio).decode('utf-8'),
                    'text': greeting_text,
                    'customer_intent': 'greeting',
                    'next_action': 'continue'
                }
                await websocket.send(json.dumps(greeting_message))
                logger.info(f"📤 Sent Policybazaar greeting to dialer {session_id}")
                
        except Exception as e:
            logger.error(f"❌ Error sending greeting: {e}")

    async def send_session_end_signal(self, websocket: WebSocketServerProtocol, session_id: str, reason: str = 'completed'):
        """Send session end signal to dialer"""
        try:
            end_signal = {
                'type': 'signal',
                'data': '__SESSION_END__',
                'reason': reason
            }
            await websocket.send(json.dumps(end_signal))
            logger.info(f"📞 Sent session end signal to dialer {session_id}: {reason}")
            
        except Exception as e:
            logger.error(f"❌ Error sending session end signal: {e}")

    # ============================================================================
    # WEB SOCKET HANDLERS
    # ============================================================================
    
    async def handle_websocket(self, websocket: WebSocketServerProtocol, path: str):
        """Main WebSocket handler for all connections"""
        session_id = f"session_{id(websocket)}"
        
        try:
            self.active_connections += 1
            self.total_connections += 1
            logger.info(f"🔌 New WebSocket connection: {session_id} from {websocket.remote_address}")
            logger.info(f"📊 WebSocket path: {path}")
            
            # Initialize session
            self.conversation_context[session_id] = []
            logger.info(f"🔌 Handling connection for {session_id}")
            
            async for message in websocket:
                try:
                    # Parse message
                    data = json.loads(message)
                    logger.info(f"📥 Received message #{len(self.conversation_context.get(session_id, [])) + 1} from {session_id} (size: {len(message)} bytes)")
                    logger.info(f"✅ JSON parsed successfully for {session_id}")
                    
                    # Determine message type
                    message_type = data.get('type', 'unknown')
                    message_data = data.get('data', '')
                    
                    logger.info(f"📋 Message type: {message_type} for {session_id}")
                    
                    # Handle different message types
                    if message_type == 'audio':
                        await self._handle_audio_message(websocket, session_id, data)
                    elif message_type == 'text':
                        await self._handle_text_message(websocket, session_id, data)
                    elif message_type == 'signal':
                        await self._handle_signal_message(websocket, session_id, data)
                    elif message_type == 'interrupt':
                        await self._handle_interrupt_message(websocket, session_id, data)
                    elif data.get('event') == 'start':
                        await self._handle_start_event(websocket, session_id, data)
                    else:
                        logger.warning(f"⚠️ Unknown message type: {message_type}")
                    
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Invalid JSON from {session_id}: {e}")
                except Exception as e:
                    logger.error(f"❌ Error handling message: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"📞 Connection closed: {session_id}")
        except Exception as e:
            logger.error(f"❌ Fatal error in connection: {e}")
        finally:
            # Cleanup
            self.active_connections -= 1
            if session_id in self.conversation_context:
                del self.conversation_context[session_id]
            logger.info(f"🧹 Cleaned up session: {session_id}")

    async def _handle_audio_message(self, websocket: WebSocketServerProtocol, session_id: str, data: dict):
        """Handle audio messages from UI or dialer"""
        try:
            audio_b64 = data.get('data', '')
            if not audio_b64:
                logger.warning(f"⚠️ No audio data in message from {session_id}")
                return
            
            audio_chunk = base64.b64decode(audio_b64)
            logger.info(f"🎤 Received audio chunk: {len(audio_chunk)} bytes from {session_id}")
            
            # Check if this is from dialer integration or normal conversation
            # We'll use the session context to determine the mode
            if not hasattr(self, '_session_modes'):
                self._session_modes = {}
            
            # Default to normal conversation mode for UI
            if session_id not in self._session_modes:
                self._session_modes[session_id] = 'normal_conversation'
            
            # Check if this is a dialer integration session (has greeting sent)
            if hasattr(self, '_greeting_sent') and session_id in self._greeting_sent and self._greeting_sent[session_id]:
                self._session_modes[session_id] = 'dialer_integration'
            
            logger.info(f"🎯 Session {session_id} mode: {self._session_modes[session_id]}")
            
            # Route based on session mode
            if self._session_modes[session_id] == 'dialer_integration':
                await self._process_dialer_audio(websocket, session_id, audio_chunk)
            else:
                await self._process_ui_audio(websocket, session_id, audio_chunk)
                
        except Exception as e:
            logger.error(f"❌ Error handling audio message: {e}")

    async def _process_ui_audio(self, websocket: WebSocketServerProtocol, session_id: str, audio_chunk: bytes):
        """Process UI audio with real-time Voice Activity Detection - Full AI conversation mode"""
        try:
            # Get current timestamp
            timestamp_ms = int(time.time() * 1000)
            
            # Process audio chunk with VAD
            vad_result = self.vad.process_audio_chunk(session_id, audio_chunk, timestamp_ms)
            
            logger.info(f"🎤 VAD result for {session_id}: speaking={vad_result['is_speaking']}, process={vad_result['should_process']}, duration={vad_result['speech_duration']}ms")
            
            # If VAD says we should process the audio
            if vad_result['should_process']:
                # Get accumulated audio buffer
                audio_buffer = self.vad.get_audio_buffer(session_id)
                
                if len(audio_buffer) > 0:
                    logger.info(f"🎤 Processing UI audio: {len(audio_buffer)} bytes from {session_id}")
                    
                    # Convert UI audio format
                    converted_audio = await self.convert_ui_audio_format(audio_buffer, "webm")
                    
                    # Use full AI conversation for UI (Phase 2)
                    response = await self.process_ui_audio_full_ai(converted_audio, session_id)
                    logger.info(f"✅ Processed UI audio with full AI, response: {response is not None}")
                    
                    if response:
                        # Send response back to UI
                        response_message = {
                            'type': 'audio',
                            'data': response['audio_b64'],
                            'text': response.get('ai_response', ''),
                            'customer_intent': response.get('transcribed_text', ''),
                            'next_action': response.get('action', 'continue')
                        }
                        await websocket.send(json.dumps(response_message))
                        logger.info(f"📤 Sent UI audio response to {session_id}")
                        
                        # Check if session should end
                        if response.get('action') == 'end_call':
                            return
                    
                    # Clear buffer after processing
                    self.vad.clear_buffer(session_id)
                    
        except Exception as e:
            logger.error(f"❌ Error processing UI audio: {e}")

    async def process_ui_audio_full_ai(self, audio_data: bytes, session_id: str) -> dict:
        """Process UI audio with full AI conversation (Phase 2)"""
        try:
            logger.info(f"🎤 Processing UI audio with full AI for {session_id} ({len(audio_data)} bytes)")
            
            # Create recognition audio object
            audio = speech_v1.RecognitionAudio(content=audio_data)
            
            # Perform speech recognition
            response = self.speech_client.recognize(
                config=self.speech_config,
                audio=audio
            )
            
            if not response.results:
                logger.warning(f"⚠️ No speech detected in UI audio for {session_id}")
                return None
            
            # Get transcribed text
            transcript = response.results[0].alternatives[0].transcript
            confidence = response.results[0].alternatives[0].confidence
            logger.info(f"📝 UI transcription: '{transcript}' (confidence: {confidence:.2f})")
            
            # Phase 2: Generate full AI response using Vertex AI
            ai_response = await self.generate_ai_response(transcript, session_id)
            
            if ai_response:
                # Convert to speech
                response_audio = await self.text_to_speech(ai_response)
                
                if response_audio:
                    return {
                        'audio_b64': base64.b64encode(response_audio).decode('utf-8'),
                        'transcribed_text': transcript,
                        'ai_response': ai_response,
                        'action': 'continue',  # Always continue for full conversations
                        'reason': 'ai_conversation'
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error processing UI audio with full AI: {e}")
            return None

    async def _process_dialer_audio(self, websocket: WebSocketServerProtocol, session_id: str, audio_chunk: bytes):
        """Process dialer audio with real-time Voice Activity Detection"""
        try:
            # Get current timestamp
            timestamp_ms = int(time.time() * 1000)
            
            # Process audio chunk with VAD
            vad_result = self.vad.process_audio_chunk(session_id, audio_chunk, timestamp_ms)
            
            logger.info(f"🎤 VAD result for {session_id}: speaking={vad_result['is_speaking']}, process={vad_result['should_process']}, duration={vad_result['speech_duration']}ms")
            
            # If VAD says we should process the audio
            if vad_result['should_process']:
                # Get accumulated audio buffer
                audio_buffer = self.vad.get_audio_buffer(session_id)
                
                if len(audio_buffer) > 0:
                    logger.info(f"🎤 Processing dialer audio: {len(audio_buffer)} bytes from {session_id}")
                    
                    # Convert audio format - UI sends WebM, not 8kHz PCM
                    converted_audio = await self.convert_ui_audio_format(audio_buffer, "webm")
                    
                    # Process the audio
                    response = await self.process_dialer_audio(converted_audio, session_id)
                    logger.info(f"✅ Processed dialer audio, response: {response is not None}")
                    
                    if response:
                        # Send audio response back to dialer
                        response_message = {
                            'type': 'audio',
                            'data': response['audio_b64'],
                            'text': response.get('ai_response', ''),
                            'customer_intent': response.get('transcribed_text', ''),
                            'next_action': response.get('action', 'continue')
                        }
                        await websocket.send(json.dumps(response_message))
                        logger.info(f"📤 Sent audio response to dialer {session_id}")
                        
                        # Check if session should end
                        if response.get('action') == 'end_call':
                            await self.send_session_end_signal(websocket, session_id, response.get('reason', 'call_completed'))
                            return
                    
                    # Clear buffer after processing
                    self.vad.clear_buffer(session_id)
                    
        except Exception as e:
            logger.error(f"❌ Error processing dialer audio: {e}")

    async def _handle_text_message(self, websocket: WebSocketServerProtocol, session_id: str, data: dict):
        """Handle text messages from UI - Full AI conversation mode"""
        try:
            text_input = data.get('data', '')
            if not text_input:
                return
            
            logger.info(f"📝 Received text input from UI: '{text_input}'")
            
            # Send greeting if not sent yet
            if not hasattr(self, '_greeting_sent'):
                self._greeting_sent = {}
            
            if session_id not in self._greeting_sent or not self._greeting_sent[session_id]:
                self._greeting_sent[session_id] = True
                await self.send_policybazaar_greeting(websocket, session_id)
            
            # Phase 2: Generate full AI response using Vertex AI
            ai_response = await self.generate_ai_response(text_input, session_id)
            
            if ai_response:
                # Generate audio response
                response_audio = await self.text_to_speech(ai_response)
                
                if response_audio:
                    # Send response back to UI
                    response_message = {
                        'type': 'audio',
                        'data': base64.b64encode(response_audio).decode('utf-8'),
                        'text': ai_response,
                        'customer_intent': 'ai_conversation',
                        'next_action': 'continue'
                    }
                    await websocket.send(json.dumps(response_message))
                    logger.info(f"📤 Sent AI response to UI {session_id}: {ai_response}")
                    
        except Exception as e:
            logger.error(f"❌ Error handling text message: {e}")

    async def _handle_signal_message(self, websocket: WebSocketServerProtocol, session_id: str, data: dict):
        """Handle signal messages"""
        try:
            signal_data = data.get('data', '')
            
            if signal_data == '__INIT__':
                logger.info(f"🚀 Init signal from UI {session_id} - sending greeting")
                await self.send_policybazaar_greeting(websocket, session_id)
            elif signal_data == '__TIMEOUT__':
                logger.info(f"⏰ Timeout signal from dialer {session_id}")
                # Handle timeout - send a prompt or retry
                timeout_response = await self._handle_dialer_timeout(session_id)
                if timeout_response:
                    response_message = {
                        'type': 'audio',
                        'data': timeout_response['audio_b64']
                    }
                    await websocket.send(json.dumps(response_message))
            elif signal_data == '__SESSION_END__':
                logger.info(f"📞 Session end signal from dialer {session_id}")
                return
                    
        except Exception as e:
            logger.error(f"❌ Error handling signal message: {e}")

    async def _handle_interrupt_message(self, websocket: WebSocketServerProtocol, session_id: str, data: dict):
        """Handle interrupt messages from UI when user interrupts AI speech"""
        try:
            interrupt_reason = data.get('data', 'user_interrupted')
            logger.info(f"🛑 Interrupt signal received from {session_id}: {interrupt_reason}")

            # Clear any ongoing audio processing for this session
            if hasattr(self, 'vad') and self.vad:
                self.vad.clear_buffer(session_id)
                logger.info(f"🧹 Cleared audio buffer for interrupted session {session_id}")

            # Send acknowledgment back to client
            ack_message = {
                'type': 'interrupt_ack',
                'data': 'interrupt_received',
                'session_id': session_id
            }
            await websocket.send(json.dumps(ack_message))
            logger.info(f"✅ Sent interrupt acknowledgment to {session_id}")

        except Exception as e:
            logger.error(f"❌ Error handling interrupt message: {e}")

    async def _handle_start_event(self, websocket: WebSocketServerProtocol, session_id: str, data: dict):
        """Handle start event from dialer"""
        try:
            logger.info(f"🚀 Start event received from dialer {session_id}")
            
            # Set session mode to dialer integration
            if not hasattr(self, '_session_modes'):
                self._session_modes = {}
            self._session_modes[session_id] = 'dialer_integration'
            logger.info(f"🎯 Set session {session_id} to dialer_integration mode")
            
            # Send greeting immediately
            if not hasattr(self, '_greeting_sent'):
                self._greeting_sent = {}
            
            if session_id not in self._greeting_sent or not self._greeting_sent[session_id]:
                self._greeting_sent[session_id] = True
                await self.send_policybazaar_greeting(websocket, session_id)
                
        except Exception as e:
            logger.error(f"❌ Error handling start event: {e}")

    async def _handle_dialer_timeout(self, session_id: str) -> dict:
        """Handle dialer timeout"""
        try:
            # Send a retry prompt
            prompt_text = "I'm sorry, I didn't hear you. Are you looking for assistance with your insurance needs today?"
            
            response_audio = await self.text_to_speech(prompt_text)
            
            if response_audio:
                return {
                    'audio_b64': base64.b64encode(response_audio).decode('utf-8'),
                    'ai_response': prompt_text
                }
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error handling dialer timeout: {e}")
            return None

    # ============================================================================
    # SERVER MANAGEMENT
    # ============================================================================
    
    async def start_server(self):
        """Start the WebSocket server"""
        try:
            logger.info("🔧 Initializing WebSocket server...")
            
            # Start WebSocket server
            server = await websockets.serve(
                self.handle_websocket,
                SERVER_CONFIG["websocket"]["host"],
                SERVER_CONFIG["websocket"]["port"],
                ping_interval=SERVER_CONFIG["websocket"]["ping_interval"],
                ping_timeout=SERVER_CONFIG["websocket"]["ping_timeout"]
            )
            
            logger.info("🌐 Voice Bot WebSocket server started successfully!")
            logger.info(f"📍 Server address: ws://{SERVER_CONFIG['websocket']['host']}:{SERVER_CONFIG['websocket']['port']}")
            logger.info(f"⚙️ Ping interval: {SERVER_CONFIG['websocket']['ping_interval']}s")
            logger.info(f"⏱️ Ping timeout: {SERVER_CONFIG['websocket']['ping_timeout']}s")
            logger.info("🎤 Ready to receive audio chunks from dialer team...")
            logger.info("📈 Server is now listening for connections...")
            
            # Start status logging
            asyncio.create_task(self._log_server_status())
            
            # Keep server running
            await server.wait_closed()
            
        except Exception as e:
            logger.error(f"❌ Failed to start server: {e}")
            raise

    async def _log_server_status(self):
        """Log server status periodically"""
        while True:
            try:
                await asyncio.sleep(30)  # Log every 30 seconds
                logger.info(f"📊 Server Status - Active connections: {self.active_connections}, Total connections: {self.total_connections}")
            except Exception as e:
                logger.error(f"❌ Error in status logging: {e}")


# ============================================================================
# MAIN FUNCTION
# ============================================================================

async def main():
    """Main function to start the voice bot"""
    try:
        logger.info("🎤 PolicyBazaar Voice Bot - Professional Outbound Call System")
        logger.info("=" * 80)
        logger.info("📋 Phase 1: Simple intent detection (YES/NO) with hardcoded responses")
        logger.info("📋 Phase 2: Full AI conversations (future implementation)")
        logger.info("=" * 80)
        
        # Create voice bot instance
        voice_bot = PolicyBazaarVoiceBot()
        logger.info("✅ Voice bot instance created successfully")
        
        # Start server
        await voice_bot.start_server()
        
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Voice bot stopped")
    except Exception as e:
        logger.error(f"❌ Failed to start voice bot: {e}")
        sys.exit(1) 