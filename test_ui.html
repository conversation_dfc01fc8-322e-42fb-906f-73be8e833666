<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Policybazaar Voice Bot - Test Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 5px;
            font-size: 28px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .status {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 25px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-indicator.disconnected {
            background-color: #dc3545;
        }

        .status-indicator.connected {
            background-color: #28a745;
        }

        .status-indicator.recording {
            background-color: #ffc107;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .tab-container {
            margin-bottom: 25px;
        }

        .tab-buttons {
            display: flex;
            gap: 0;
            border-radius: 10px;
            overflow: hidden;
            border: 2px solid #e9ecef;
        }

        .tab-button {
            background: #f8f9fa;
            color: #666;
            border: none;
            padding: 15px 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .tab-button:hover {
            background: #e9ecef;
            color: #333;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 25px;
        }

        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .button.success {
            background: #28a745;
        }

        .button.success:hover {
            background: #1e7e34;
        }

        .button.danger {
            background: #dc3545;
        }

        .button.danger:hover {
            background: #c82333;
        }

        .button.recording {
            background: #ffc107;
            color: #212529;
        }

        .conversation {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 8px;
            max-width: 80%;
        }

        .message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
        }

        .message.ai {
            background: #e9ecef;
            color: #333;
        }

        .message.system {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            font-size: 14px;
        }

        .sender {
            font-weight: 600;
            margin-bottom: 5px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .content {
            line-height: 1.4;
        }

        .icon {
            font-size: 16px;
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .quick-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-button:hover {
            background: #5a6268;
        }

        .quick-button.success {
            background: #28a745;
        }

        .quick-button.danger {
            background: #dc3545;
        }

        .quick-button.secondary {
            background: #2196F3;
            color: white;
        }

        .quick-button.secondary:hover {
            background: #1976D2;
        }

        /* Voice Activity Indicator Styles */
        .voice-activity-indicator {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 12px;
            margin: 20px 0;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .voice-activity-indicator.active {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
        }

        .voice-activity-indicator.ai-speaking {
            border-color: #2196F3;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
        }

        .voice-level-bar {
            width: 200px;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }

        .voice-level {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 4px;
            width: 0%;
            transition: width 0.1s ease;
        }

        .voice-level.ai-speaking {
            background: linear-gradient(90deg, #2196F3, #03A9F4);
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .voice-status {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
            min-width: 100px;
        }

        .voice-status.listening {
            color: #4CAF50;
        }

        .voice-status.ai-speaking {
            color: #2196F3;
        }

        .voice-status.processing {
            color: #FF9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 PolicyBazaar Voice Assistant</h1>
            <p>Professional AI-Powered Customer Service</p>
        </div>

        <div class="status">
            <span class="status-indicator disconnected" id="statusIndicator"></span>
            <span id="statusText">Disconnected</span>
        </div>

        <div class="tab-container">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="switchTab('dialer')" id="dialerTab">
                    📞 Dialer Integration
                </button>
                <button class="tab-button" onclick="switchTab('conversation')" id="conversationTab">
                    💬 Normal Conversation
                </button>
            </div>
        </div>

        <!-- Dialer Integration Tab -->
        <div class="tab-content active" id="dialerTabContent">
        <div class="controls">
                <button class="button" id="dialerStartBtn" onclick="startDialerTest()">
                    <span class="icon">📞</span>
                    Start Call Simulation
            </button>

                <button class="button danger" id="dialerStopBtn" onclick="stopDialerTest()" disabled>
                    <span class="icon">📞</span>
                    End Call
            </button>
        </div>

            <div class="conversation" id="dialerConversation">
            <div class="message system">
                    <div class="content">Click "Start Call Simulation" to begin. The AI will initiate the conversation and you can respond naturally using voice.</div>
                </div>
            </div>

            <div class="voice-activity-indicator" id="dialerVoiceIndicator" style="display: none;">
                <div class="voice-level-bar">
                    <div class="voice-level" id="dialerVoiceLevel"></div>
                </div>
                <span class="voice-status" id="dialerVoiceStatus">Listening...</span>
            </div>

            <div class="quick-actions" id="dialerQuickActions" style="display: none;">
                <button class="quick-button secondary" onclick="toggleContinuousMode('dialer')">🎤 Enable Smart Voice Mode</button>
                <button class="quick-button" onclick="simulateResponse('yes')">✅ Simulate: Interested</button>
                <button class="quick-button danger" onclick="simulateResponse('no')">❌ Simulate: Not Interested</button>
            </div>
        </div>

        <!-- Normal Conversation Tab -->
        <div class="tab-content" id="conversationTabContent">
            <div class="controls">
                <button class="button" id="convStartBtn" onclick="startConversation()">
                <span class="icon">🎙️</span>
                    Start Conversation
            </button>
            
                <button class="button danger" id="convStopBtn" onclick="stopConversation()" disabled>
                <span class="icon">⏹️</span>
                    End Conversation
            </button>
        </div>

            <div class="conversation" id="conversationLog">
                <div class="message system">
                    <div class="content">Click "Start Conversation" to begin a normal conversation. You can speak first and have a natural dialogue with the AI.</div>
                </div>
            </div>

            <div class="voice-activity-indicator" id="convVoiceIndicator" style="display: none;">
                <div class="voice-level-bar">
                    <div class="voice-level" id="convVoiceLevel"></div>
                </div>
                <span class="voice-status" id="convVoiceStatus">Listening...</span>
            </div>

            <div class="quick-actions" id="convQuickActions" style="display: none;">
                <button class="quick-button" onclick="sendTextMessage('Tell me about health insurance')">🏥 Health Insurance</button>
                <button class="quick-button" onclick="sendTextMessage('What car insurance options do you have?')">🚗 Car Insurance</button>
                <button class="quick-button" onclick="sendTextMessage('I need life insurance advice')">👨‍👩‍👧‍👦 Life Insurance</button>
                <button class="quick-button secondary" onclick="toggleContinuousMode('conversation')">🎤 Toggle Voice Mode</button>
            </div>
        </div>
    </div>

    <script>
        let websocket = null;
        let mediaRecorder = null;
        let audioChunks = [];
        let isConnected = false;
        let isRecording = false;
        let currentTab = 'dialer';
        let continuousStream = null;
        let isAISpeaking = false;
        let audioContext = null;
        let analyser = null;
        let voiceActivityThreshold = 0.008;  // Matched with backend for consistency
        let silenceTimeout = null;
        let isContinuousMode = false;

        // DOM elements
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const dialerStartBtn = document.getElementById('dialerStartBtn');
        const dialerStopBtn = document.getElementById('dialerStopBtn');
        const convStartBtn = document.getElementById('convStartBtn');
        const convStopBtn = document.getElementById('convStopBtn');
        const dialerConversation = document.getElementById('dialerConversation');
        const conversationLog = document.getElementById('conversationLog');
        const dialerQuickActions = document.getElementById('dialerQuickActions');
        const convQuickActions = document.getElementById('convQuickActions');

        function updateStatus(status, text) {
            statusIndicator.className = `status-indicator ${status}`;
            statusText.textContent = text;
        }

        function addMessage(conversationElement, sender, content, type = 'system') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const senderDiv = document.createElement('div');
            senderDiv.className = 'sender';
            senderDiv.textContent = sender;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(senderDiv);
            messageDiv.appendChild(contentDiv);
            conversationElement.appendChild(messageDiv);
            conversationElement.scrollTop = conversationElement.scrollHeight;
        }

        function switchTab(tabName) {
            // Hide all tab contents
            document.getElementById('dialerTabContent').classList.remove('active');
            document.getElementById('conversationTabContent').classList.remove('active');
            
            // Remove active class from all tab buttons
            document.getElementById('dialerTab').classList.remove('active');
            document.getElementById('conversationTab').classList.remove('active');
            
            // Show selected tab content
            document.getElementById(tabName + 'TabContent').classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
            
            currentTab = tabName;
        }

        // ===== DIALER INTEGRATION FUNCTIONS =====
        
        async function startDialerTest() {
            try {
                updateStatus('connected', 'Starting dialer test...');
                addMessage(dialerConversation, 'System', 'Starting dialer integration test...', 'system');
                
                // Connect to WebSocket
                websocket = new WebSocket('ws://localhost:8765/ws');
                
                websocket.onopen = function(event) {
                    isConnected = true;
                    updateStatus('connected', 'Connected - Dialer test active');
                    addMessage(dialerConversation, 'System', 'Connected to voice bot', 'system');
                    
                    // Send start event (like dialer.py does)
                    const startEvent = {
                        event: "start",
                        sequence_number: 1,
                        stream_id: "test_call_123",
                        bytes: null,
                        start: {
                            stream_sid: "test_call_123",
                            user_name: "Test User",
                            flow_name: "test_flow",
                            call_sid: "test_call_123",
                            account_sid: "test_account",
                            voice_name: "test_voice"
                        }
                    };
                    
                    websocket.send(JSON.stringify(startEvent));
                    addMessage(dialerConversation, 'System', 'Sent start event - AI will speak first', 'system');
                    
                    dialerStartBtn.disabled = true;
                    dialerStopBtn.disabled = false;
                };

                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('Received:', data);

                        if (data.type === 'audio' && data.data) {
                            // Set AI speaking state
                            isAISpeaking = true;

                            // Play received audio (AI response)
                            const audioData = atob(data.data);
                            const audioArray = new Uint8Array(audioData.length);
                            for (let i = 0; i < audioData.length; i++) {
                                audioArray[i] = audioData.charCodeAt(i);
                            }

                            // Convert to playable audio
                            const audioBlob = new Blob([audioArray], { type: 'audio/wav' });
                            const audioUrl = URL.createObjectURL(audioBlob);
                            const audio = new Audio(audioUrl);

                            // Store current audio for interruption
                            window.currentAIAudio = audio;

                            audio.play().then(() => {
                                console.log('Playing AI response');
                            }).catch(error => {
                                console.error('Error playing audio:', error);
                            });

                            // Reset AI speaking state when audio ends
                            audio.onended = function() {
                                isAISpeaking = false;
                                window.currentAIAudio = null;
                                console.log('AI finished speaking');
                            };

                            // Handle audio interruption
                            audio.onerror = function() {
                                isAISpeaking = false;
                                window.currentAIAudio = null;
                                console.log('AI audio interrupted or error');
                            };

                            // Add to conversation
                            const text = data.text || 'AI response (audio only)';
                            addMessage(dialerConversation, 'AI Assistant', text, 'ai');

                            // Show quick actions after AI speaks
                            dialerQuickActions.style.display = 'flex';
                        } else if (data.type === 'signal') {
                            addMessage(dialerConversation, 'System', `Signal: ${data.data}`, 'system');
                        }
                    } catch (error) {
                        console.error('Error parsing message:', error);
                        addMessage(dialerConversation, 'System', 'Received non-JSON message', 'system');
                    }
                };

                websocket.onclose = function(event) {
                    isConnected = false;
                    updateStatus('disconnected', 'Dialer test ended');
                    addMessage(dialerConversation, 'System', 'Dialer test ended', 'system');
                    resetDialerUI();
                };

                websocket.onerror = function(error) {
                    console.error('WebSocket error:', error);
                    updateStatus('disconnected', 'Connection error');
                    addMessage(dialerConversation, 'System', 'Connection error - make sure voice bot is running', 'system');
                    resetDialerUI();
                };

            } catch (error) {
                console.error('Connection error:', error);
                updateStatus('disconnected', 'Connection failed');
                addMessage(dialerConversation, 'System', 'Failed to connect - make sure voice bot is running', 'system');
            }
        }

        function stopDialerTest() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
            isConnected = false;
            resetDialerUI();
            addMessage(dialerConversation, 'System', 'Dialer test stopped', 'system');
        }

        function resetDialerUI() {
            dialerStartBtn.disabled = false;
            dialerStopBtn.disabled = true;
            dialerQuickActions.style.display = 'none';
        }

        function simulateResponse(type) {
            if (!isConnected || !websocket) {
                addMessage(dialerConversation, 'System', 'Not connected', 'system');
                return;
            }

            let text = '';
            switch (type) {
                case 'yes':
                    text = 'Yes, I am interested';
                    break;
                case 'no':
                    text = 'No, not interested';
                    break;
                case 'unclear':
                    text = 'Sorry, what?';
                    break;
            }
            
            // Send text response
            const message = {
                type: 'text',
                data: text
            };
            
            websocket.send(JSON.stringify(message));
            addMessage(dialerConversation, 'You', text, 'user');
        }

        // ===== NORMAL CONVERSATION FUNCTIONS =====
        
        async function startConversation() {
            try {
                updateStatus('connected', 'Starting conversation...');
                addMessage(conversationLog, 'System', 'Starting normal conversation...', 'system');
                
                // Connect to WebSocket
                websocket = new WebSocket('ws://localhost:8765/ws');
                
                websocket.onopen = function(event) {
                    isConnected = true;
                    updateStatus('connected', 'Connected - Ready for conversation');
                    addMessage(conversationLog, 'System', 'Connected to voice bot - You can speak first', 'system');
                    
                    convStartBtn.disabled = true;
                    convStopBtn.disabled = false;
                    convQuickActions.style.display = 'flex';
                };

                websocket.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('Received:', data);

                        if (data.type === 'audio' && data.data) {
                            // Set AI speaking state
                            isAISpeaking = true;

                            // Play received audio (AI response)
                            const audioData = atob(data.data);
                            const audioArray = new Uint8Array(audioData.length);
                            for (let i = 0; i < audioData.length; i++) {
                                audioArray[i] = audioData.charCodeAt(i);
                            }

                            // Convert to playable audio
                            const audioBlob = new Blob([audioArray], { type: 'audio/wav' });
                            const audioUrl = URL.createObjectURL(audioBlob);
                            const audio = new Audio(audioUrl);

                            // Store current audio for interruption
                            window.currentAIAudio = audio;

                            audio.play().then(() => {
                                console.log('Playing AI response');
                            }).catch(error => {
                                console.error('Error playing audio:', error);
                            });

                            // Reset AI speaking state when audio ends
                            audio.onended = function() {
                                isAISpeaking = false;
                                window.currentAIAudio = null;
                                console.log('AI finished speaking');
                            };

                            // Handle audio interruption
                            audio.onerror = function() {
                                isAISpeaking = false;
                                window.currentAIAudio = null;
                                console.log('AI audio interrupted or error');
                            };

                            // Add to conversation
                            const text = data.text || 'AI response (audio only)';
                            addMessage(conversationLog, 'AI Assistant', text, 'ai');

                            // Show transcribed text if available
                            if (data.customer_intent && data.customer_intent !== 'ai_conversation') {
                                addMessage(conversationLog, 'System', `You said: "${data.customer_intent}"`, 'system');
                            }
                        } else if (data.type === 'error') {
                            addMessage(conversationLog, 'System', `Error: ${data.error}`, 'system');
                        }
                    } catch (error) {
                        console.error('Error parsing message:', error);
                        addMessage(conversationLog, 'System', 'Received non-JSON message', 'system');
                    }
                };

                websocket.onclose = function(event) {
                    isConnected = false;
                    updateStatus('disconnected', 'Conversation ended');
                    addMessage(conversationLog, 'System', 'Conversation ended', 'system');
                    resetConversationUI();
                };

                websocket.onerror = function(error) {
                    console.error('WebSocket error:', error);
                    updateStatus('disconnected', 'Connection error');
                    addMessage(conversationLog, 'System', 'Connection error - make sure voice bot is running', 'system');
                    resetConversationUI();
                };

            } catch (error) {
                console.error('Connection error:', error);
                updateStatus('disconnected', 'Connection failed');
                addMessage(conversationLog, 'System', 'Failed to connect - make sure voice bot is running', 'system');
            }
        }

        function stopConversation() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
            isConnected = false;
            resetConversationUI();
            addMessage(conversationLog, 'System', 'Conversation stopped', 'system');
        }

        function resetConversationUI() {
            convStartBtn.disabled = false;
            convStopBtn.disabled = true;
            convQuickActions.style.display = 'none';
        }

        function sendTextMessage(text) {
            if (!isConnected || !websocket) {
                addMessage(conversationLog, 'System', 'Not connected', 'system');
                return;
            }

            // Send as text message
            const message = {
                type: 'text',
                data: text
            };
            
            websocket.send(JSON.stringify(message));
            addMessage(conversationLog, 'You', text, 'user');
        }

        // ===== CONTINUOUS AUDIO STREAMING FUNCTIONS =====

        async function startContinuousAudio(mode) {
            try {
                console.log(`Starting continuous audio for ${mode} mode`);

                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                continuousStream = stream;

                // Set up audio context for voice activity detection
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const source = audioContext.createMediaStreamSource(stream);
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 256;
                source.connect(analyser);

                // Set up MediaRecorder for continuous streaming
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm'
                });

                let isStreaming = true;

                mediaRecorder.ondataavailable = async function(event) {
                    if (event.data.size > 0 && isStreaming && !isAISpeaking) {
                        // Only send audio when not in AI speaking mode
                        if (mode === 'dialer') {
                            await sendDialerAudioChunk(event.data);
                        } else {
                            await sendConversationAudioChunk(event.data);
                        }
                    }
                };

                mediaRecorder.onstop = function() {
                    isStreaming = false;
                    console.log('Continuous recording stopped');
                };

                // Start continuous recording with small chunks
                mediaRecorder.start(100);

                // Start voice activity monitoring
                startVoiceActivityMonitoring(mode);

                // Show voice indicator
                const indicator = document.getElementById(mode === 'dialer' ? 'dialerVoiceIndicator' : 'convVoiceIndicator');
                indicator.style.display = 'flex';

                isContinuousMode = true;
                console.log(`Continuous audio started for ${mode}`);

            } catch (error) {
                console.error('Error starting continuous audio:', error);
                alert('Could not access microphone. Please check permissions.');
            }
        }

        function stopContinuousAudio(mode) {
            try {
                if (mediaRecorder && mediaRecorder.state === 'recording') {
                    mediaRecorder.stop();
                }

                if (continuousStream) {
                    continuousStream.getTracks().forEach(track => track.stop());
                    continuousStream = null;
                }

                if (audioContext) {
                    audioContext.close();
                    audioContext = null;
                }

                // Hide voice indicator
                const indicator = document.getElementById(mode === 'dialer' ? 'dialerVoiceIndicator' : 'convVoiceIndicator');
                indicator.style.display = 'none';

                isContinuousMode = false;
                console.log(`Continuous audio stopped for ${mode}`);

            } catch (error) {
                console.error('Error stopping continuous audio:', error);
            }
        }

        function startVoiceActivityMonitoring(mode) {
            const indicator = document.getElementById(mode === 'dialer' ? 'dialerVoiceIndicator' : 'convVoiceIndicator');
            const voiceLevel = document.getElementById(mode === 'dialer' ? 'dialerVoiceLevel' : 'convVoiceLevel');
            const voiceStatus = document.getElementById(mode === 'dialer' ? 'dialerVoiceStatus' : 'convVoiceStatus');

            function updateVoiceActivity() {
                if (!analyser || !isContinuousMode) return;

                const dataArray = new Uint8Array(analyser.frequencyBinCount);
                analyser.getByteFrequencyData(dataArray);

                // Calculate average volume
                const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
                const normalizedLevel = Math.min(average / 128, 1); // Normalize to 0-1

                // Update visual indicator
                voiceLevel.style.width = `${normalizedLevel * 100}%`;

                // Handle AI interruption when user starts speaking
                if (normalizedLevel > voiceActivityThreshold && isAISpeaking) {
                    console.log('User interrupting AI - stopping AI audio');
                    interruptAI();
                }

                // Update status based on voice activity and AI state
                if (isAISpeaking) {
                    indicator.className = 'voice-activity-indicator ai-speaking';
                    voiceLevel.className = 'voice-level ai-speaking';
                    voiceStatus.textContent = 'AI Speaking...';
                    voiceStatus.className = 'voice-status ai-speaking';
                } else if (normalizedLevel > voiceActivityThreshold) {
                    indicator.className = 'voice-activity-indicator active';
                    voiceLevel.className = 'voice-level';
                    voiceStatus.textContent = 'You are speaking';
                    voiceStatus.className = 'voice-status listening';
                } else {
                    indicator.className = 'voice-activity-indicator';
                    voiceLevel.className = 'voice-level';
                    voiceStatus.textContent = 'Listening...';
                    voiceStatus.className = 'voice-status';
                }

                requestAnimationFrame(updateVoiceActivity);
            }

            updateVoiceActivity();
        }

        function toggleContinuousMode(mode) {
            if (isContinuousMode) {
                stopContinuousAudio(mode);
                const button = event.target;
                button.textContent = '🎤 Start Voice Mode';
                button.classList.remove('success');
                button.classList.add('secondary');
            } else {
                startContinuousAudio(mode);
                const button = event.target;
                button.textContent = '🔇 Stop Voice Mode';
                button.classList.remove('secondary');
                button.classList.add('success');
            }
        }

        function interruptAI() {
            if (window.currentAIAudio && !window.currentAIAudio.paused) {
                console.log('Interrupting AI audio playback');
                window.currentAIAudio.pause();
                window.currentAIAudio.currentTime = 0;
                isAISpeaking = false;
                window.currentAIAudio = null;

                // Send interruption signal to backend if needed
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    const interruptMessage = {
                        type: 'interrupt',
                        data: 'user_interrupted'
                    };
                    websocket.send(JSON.stringify(interruptMessage));
                }
            }
        }

        // ===== RECORDING FUNCTIONS =====
        
        async function startDialerRecording() {
            if (!isConnected || !websocket) {
                addMessage(dialerConversation, 'System', 'Not connected', 'system');
                return;
            }

            try {
                updateStatus('recording', 'Recording... Speak now!');
                addMessage(dialerConversation, 'System', 'Recording started... Speak your response!', 'system');
                
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    } 
                });
                
                // Use MediaRecorder with smaller timeslice for real-time streaming
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm'
                });
                
                audioChunks = [];
                let isRecording = true;
                
                mediaRecorder.ondataavailable = async function(event) {
                    if (event.data.size > 0 && isRecording) {
                        // Send audio chunk immediately for real-time processing
                        await sendDialerAudioChunk(event.data);
                    }
                };
                
                mediaRecorder.onstop = function() {
                    isRecording = false;
                    // Stop all tracks
                    stream.getTracks().forEach(track => track.stop());
                    updateStatus('connected', 'Connected - Dialer test active');
                    addMessage(dialerConversation, 'System', 'Recording stopped', 'system');
                };
                
                // Start recording with 100ms timeslice for real-time streaming
                mediaRecorder.start(100);
                
                // Auto-stop after 30 seconds maximum
                setTimeout(() => {
                    if (mediaRecorder && mediaRecorder.state === 'recording') {
                        stopDialerRecording();
                    }
                }, 30000);
                
            } catch (error) {
                console.error('Error starting recording:', error);
                addMessage(dialerConversation, 'System', 'Error accessing microphone', 'system');
                updateStatus('connected', 'Connected - Dialer test active');
            }
        }

        function stopDialerRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                updateStatus('connected', 'Connected - Dialer test active');
                addMessage(dialerConversation, 'System', 'Recording stopped, processing...', 'system');
            }
        }

        async function sendDialerAudio(audioBlob) {
            try {
                // Convert to the format expected by voice bot
                const arrayBuffer = await audioBlob.arrayBuffer();
                const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
                
                const message = {
                    type: 'audio',
                    data: base64Audio
                };
                
                if (websocket && isConnected) {
                    websocket.send(JSON.stringify(message));
                    addMessage(dialerConversation, 'You', 'Sent voice response', 'user');
                } else {
                    addMessage(dialerConversation, 'System', 'Not connected to voice bot', 'system');
                }
                
            } catch (error) {
                console.error('Error sending audio:', error);
                addMessage(dialerConversation, 'System', 'Error sending audio to voice bot', 'system');
            }
        }

        async function sendDialerAudioChunk(audioBlob) {
            try {
                // Convert to the format expected by voice bot
                const arrayBuffer = await audioBlob.arrayBuffer();
                const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
                
                const message = {
                    type: 'audio',
                    data: base64Audio
                };
                
                if (websocket && isConnected) {
                    websocket.send(JSON.stringify(message));
                }
                
            } catch (error) {
                console.error('Error sending audio chunk:', error);
            }
        }

        async function startConversationRecording() {
            if (!isConnected || !websocket) {
                addMessage(conversationLog, 'System', 'Not connected', 'system');
                return;
            }

            try {
                updateStatus('recording', 'Recording... Speak now!');
                addMessage(conversationLog, 'System', 'Recording started... Speak to AI!', 'system');
                
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    } 
                });
                
                // Use MediaRecorder with smaller timeslice for real-time streaming
                mediaRecorder = new MediaRecorder(stream, {
                    mimeType: 'audio/webm'
                });
                
                audioChunks = [];
                let isRecording = true;
                
                mediaRecorder.ondataavailable = async function(event) {
                    if (event.data.size > 0 && isRecording) {
                        // Send audio chunk immediately for real-time processing
                        await sendConversationAudioChunk(event.data);
                    }
                };
                
                mediaRecorder.onstop = function() {
                    isRecording = false;
                    // Stop all tracks
                    stream.getTracks().forEach(track => track.stop());
                    updateStatus('connected', 'Connected - Ready for conversation');
                    addMessage(conversationLog, 'System', 'Recording stopped', 'system');
                };
                
                // Start recording with 100ms timeslice for real-time streaming
                mediaRecorder.start(100);
                
                // Auto-stop after 30 seconds maximum
                setTimeout(() => {
                    if (mediaRecorder && mediaRecorder.state === 'recording') {
                        stopConversationRecording();
                    }
                }, 30000);
                
            } catch (error) {
                console.error('Error starting recording:', error);
                addMessage(conversationLog, 'System', 'Error accessing microphone', 'system');
                updateStatus('connected', 'Connected - Ready for conversation');
            }
        }

        function stopConversationRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                updateStatus('connected', 'Connected - Ready for conversation');
                addMessage(conversationLog, 'System', 'Recording stopped, processing...', 'system');
            }
        }

        async function sendConversationAudio(audioBlob) {
            try {
                // Convert to the format expected by voice bot
                const arrayBuffer = await audioBlob.arrayBuffer();
                const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
                
            const message = {
                    type: 'audio',
                    data: base64Audio
            };
            
            if (websocket && isConnected) {
                websocket.send(JSON.stringify(message));
                    addMessage(conversationLog, 'You', 'Sent voice message', 'user');
            } else {
                    addMessage(conversationLog, 'System', 'Not connected to voice bot', 'system');
                }
                
            } catch (error) {
                console.error('Error sending audio:', error);
                addMessage(conversationLog, 'System', 'Error sending audio to voice bot', 'system');
            }
        }

        async function sendConversationAudioChunk(audioBlob) {
            try {
                // Convert to the format expected by voice bot
                const arrayBuffer = await audioBlob.arrayBuffer();
                const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
                
                const message = {
                    type: 'audio',
                    data: base64Audio
                };
                
                if (websocket && isConnected) {
                    websocket.send(JSON.stringify(message));
                }
                
            } catch (error) {
                console.error('Error sending audio chunk:', error);
            }
        }

        // Initialize
        updateStatus('disconnected', 'Ready to test - Make sure voice bot is running');
    </script>
</body>
</html> 