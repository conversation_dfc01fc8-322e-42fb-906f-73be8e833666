# Policybazaar Voice Bot - Dialer Integration Guide

## Overview

This document outlines the integration between the Policybazaar Voice Bot and the dialer team's AGI script for seamless outbound call handling. The dialer team handles call initiation, and our voice bot responds when customers answer.

## Architecture

```
[Customer Answers] → [Asterisk/AGI] ←→ [bot.py] ←→ [WebSocket] ←→ [voice_bot.py] ←→ [Google Cloud AI]
```

- **Customer**: Answers the outbound call initiated by dialer
- **Asterisk/AGI**: Handles call routing and audio streams  
- **bot.py**: Dialer team's AGI script (on dialer server)
- **voice_bot.py**: Policybazaar Voice Bot (on our server)
- **WebSocket**: Real-time bidirectional communication

## Integration Flow

### 1. Call Initiation (Dialer Team)
- Dialer system places outbound call to customer
- Customer answers the phone
- Di<PERSON><PERSON> connects to voice bot via WebSocket

### 2. Voice Bot Response  
- Voice bot detects dialer connection
- Automatically sends Policybazaar greeting on first audio chunk
- Processes customer responses and provides appropriate actions

### 3. Call Outcomes
- **Customer Interested**: "Connecting to agent" → End call
- **Customer Not Interested**: "Thank you" → End call  
- **Unclear Response**: Retry with clarification

## WebSocket Protocol

### Connection
**Endpoint**: `ws://YOUR_SERVER_IP:8765/ws`

### Message Format
```json
{
  "type": "audio|signal",
  "data": "base64_encoded_data|signal_name"
}
```

### Audio Specifications
- **Sample Rate**: 8000 Hz (8kHz)
- **Channels**: 1 (Mono) 
- **Bit Depth**: 16-bit
- **Encoding**: LINEAR16 PCM
- **Chunk Size**: 320 bytes (20ms of audio)

**Performance Optimization**: 
- ✅ **Incoming Audio**: Converted from 8kHz (dialer) → 16kHz (Google STT)
- ✅ **Outgoing Audio**: Sent in original TTS format (no conversion needed)
- 🚀 **Result**: Faster response times with optimized audio processing

## Session Flow

```javascript
// 1. Dialer connects when customer answers
WebSocket Connection Established

// 2. Dialer sends first audio chunk  
→ {
  "type": "audio",
  "data": "UklGRpK..." // Base64 encoded customer audio
}

// 3. Voice bot sends greeting automatically
← {
  "type": "audio", 
  "data": "UklGRpK..." // Base64 encoded Policybazaar greeting
}

// 4. Customer responds
→ {
  "type": "audio",
  "data": "UklGRpK..." // Base64 encoded customer response
}

// 5. Voice bot analyzes and responds
← {
  "type": "audio",
  "data": "UklGRpK..." // Base64 encoded AI response
}

// 6. Session end (if needed)
← {
  "type": "signal", 
  "data": "__SESSION_END__",
  "details": {
    "reason": "connecting_to_agent|customer_not_interested|call_completed"
  }
}
```

## Conversation Scripts

### Auto-Greeting (Sent on First Audio Chunk)
```
"Hello! This is Policybazaar, India's leading insurance marketplace. 
We noticed you recently showed interest in our insurance services. 
Would you like to speak to an insurance advisor for assistance?"
```

### Customer Responses

**Interested (Yes, Sure, Please, etc.)**
```
"Great! Connecting you to a certified insurance advisor who can assist you further. 
Please hold the line."
→ Session ends with "connecting_to_agent"
```

**Not Interested (No, Not interested, etc.)**
```
"No problem. If you need assistance in the future, feel free to reach out at 
policybazaar.com or call us anytime. Thank you for your time. Have a great day!"
→ Session ends with "customer_not_interested"
```

**Unclear Response**
```
"Sorry, I didn't catch that. Are you looking for assistance with your insurance needs today?"
→ Continues conversation
```

**Timeout Handling**
```
"I'm sorry, I didn't hear you. Are you looking for assistance with your insurance needs today?"
→ Continues conversation
```

## Dialer Team Integration

### Required Changes to bot.py

#### 1. Update WebSocket URI
```python
# In bot.py, update this line:
websocket_uri = "ws://YOUR_VOICE_BOT_SERVER_IP:8765/ws"
```

#### 2. Remove Start Event (Optional)
The `send_start_event()` function is not required as our voice bot automatically handles session initiation.

#### 3. Audio Format (Already Correct)
Your current audio format is perfect:
- 8kHz, 16-bit, mono
- 320-byte chunks
- Base64 encoded

### Current Compatibility Status
✅ **Message Format**: Compatible  
✅ **Audio Format**: Compatible
✅ **Signal Handling**: Compatible
✅ **Session Management**: Compatible
✅ **Real-time Streaming**: Compatible

**No major changes needed to your bot.py!**

## Testing

### Local Testing
```bash
# 1. Start voice bot
python voice_bot.py

# 2. Test dialer integration
python test_dialer_integration.py
```

### Verification Checklist
- [x] WebSocket connection established
- [x] Greeting sent automatically  
- [x] Audio format conversion working
- [x] Customer response analysis functional
- [x] Session end signals handled properly

## Production Deployment

### Voice Bot Server Requirements
- **CPU**: 4+ cores recommended
- **RAM**: 8GB+ recommended
- **Network**: Low latency to Google Cloud
- **Port**: 8765 (or configure as needed)

### Server Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Configure Google Cloud credentials
export GOOGLE_APPLICATION_CREDENTIALS="path/to/credentials.json"

# Start voice bot
python voice_bot.py
```

### Security Considerations
- Use WSS (WebSocket Secure) for production
- Implement firewall rules
- Monitor connection patterns
- Consider authentication if needed

## Monitoring

### Key Metrics
- Connection success rate
- Greeting delivery time
- Speech recognition accuracy
- Customer response classification accuracy
- Session completion rate

### Logging
- Location: `logs/voice_bot.log`
- Real-time monitoring: `tail -f logs/voice_bot.log`
- Session tracking with unique IDs
- Detailed audio processing logs

## Troubleshooting

### Common Issues

**1. No Speech Detected**
- Check audio format (8kHz, 16-bit, mono)
- Verify chunk size (320 bytes)
- Test with real speech (not synthetic audio)

**2. Connection Issues**
- Verify WebSocket URL
- Check firewall settings
- Monitor network latency

**3. Greeting Not Sent**
- Ensure first audio chunk triggers greeting
- Check TTS service status
- Verify audio format conversion

### Debug Commands
```bash
# Test WebSocket connection
python test_dialer_integration.py

# Monitor logs
tail -f logs/voice_bot.log

# Check server status
curl -I http://YOUR_SERVER:8765
```

## Summary

This integration provides a seamless experience where:

1. **Dialer initiates** outbound calls
2. **Customer answers** the phone  
3. **Voice bot automatically greets** with Policybazaar introduction
4. **AI analyzes responses** and provides appropriate actions
5. **Session ends** with clear next steps

The system is **production-ready** and requires **minimal changes** to your existing `bot.py` script.

---

**Contact**: For technical support, refer to logs and error messages. The integration has been tested and verified to work with your provided specifications. 