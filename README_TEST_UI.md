# 🎤 Test UI for Policybazaar Voice Bot

## 📋 Overview

This test UI simulates the dialer integration flow for the Policybazaar Voice Bot. It allows you to test the complete conversation flow without needing the actual dialer system.

## 🚀 Quick Start

### Option 1: Auto-Start (Recommended)
```bash
python3 test_ui_quick.py
```
This will:
- Start the voice bot automatically
- Open the test UI in your browser
- Provide testing instructions

### Option 2: Manual Start
```bash
# 1. Start voice bot
./start_production.sh

# 2. Open test_ui.html in your browser
# File location: file:///path/to/voice-bot1/test_ui.html
```

## 🎯 Testing Flow

### 1. **Simulate Dialer Connection**
- Click "Simulate Dialer Connection (Customer Answered)"
- This mimics the dialer team connecting when customer answers
- Voice bot will automatically send the Policybazaar greeting

### 2. **Test Customer Responses**

#### Option A: Quick Button Testing
- **Yes/Interested**: Simulates positive customer response
- **No/Not Interested**: Simulates negative customer response  
- **Unclear Response**: Simulates unclear/confused response

#### Option B: Voice Input Testing
- Click "Start Speaking" to record from your microphone
- Speak your response (e.g., "Yes, I'm interested")
- Click "Stop Speaking" to send the audio
- The system will process your speech and respond

### 3. **Monitor Responses**
- Watch the conversation log for AI responses
- Listen to the generated audio responses
- Check the system messages for intent detection
- Observe the next action recommendations

## 🔧 Features

### ✅ **Complete Flow Simulation**
- Dialer connection simulation
- Automatic AI greeting (Policybazaar intro)
- Customer response processing
- Intent classification (positive/negative/unclear)
- Appropriate AI responses
- Call ending logic

### ✅ **Testing Options**
- **Text Input**: Quick button responses for rapid testing
- **Voice Input**: Real microphone recording for speech testing
- **Audio Playback**: Hear AI responses as audio
- **Conversation Log**: Visual transcript of the entire conversation

### ✅ **Real-time Monitoring**
- Connection status indicators
- Message type identification
- Error handling and display
- Session management

## 📊 Expected Responses

### Customer Says "Yes" / "Interested"
```
AI Response: "Great! Connecting you to a certified insurance advisor who can assist you further. Please hold the line."
Next Action: agent_handover
Intent: positive
```

### Customer Says "No" / "Not Interested"
```
AI Response: "No problem. If you need assistance in the future, feel free to reach out at policybazaar.com or call us anytime. Thank you for your time. Have a great day!"
Next Action: end_call
Intent: negative
```

### Customer Says Unclear Response
```
AI Response: "Sorry, I didn't catch that. Are you looking for assistance with your insurance needs today?"
Next Action: retry
Intent: unclear
```

## 🔍 Troubleshooting

### ❌ **Connection Issues**
**Problem**: "Connection error - make sure voice bot is running"
**Solution**: 
```bash
# Check if voice bot is running
ps aux | grep voice_bot.py

# Start voice bot if not running
./start_production.sh
```

### ❌ **Microphone Issues**
**Problem**: "Error accessing microphone"
**Solution**: 
- Allow microphone permission in browser
- Check browser console for detailed errors
- Try using HTTPS (for some browsers)

### ❌ **Audio Playback Issues**
**Problem**: AI responses not playing
**Solution**: 
- Check browser audio permissions
- Ensure speakers/headphones are connected
- Check browser console for audio errors

### ❌ **WebSocket Connection Failed**
**Problem**: Cannot connect to WebSocket
**Solution**: 
```bash
# Verify voice bot is listening on port 8765
netstat -tulpn | grep 8765

# Check logs for errors
tail -f logs/voice_bot.log
```

## 📝 Testing Checklist

### ✅ **Basic Flow Testing**
- [ ] Connect successfully to voice bot
- [ ] Receive Policybazaar greeting automatically
- [ ] Test "Yes" response → Agent handover message
- [ ] Test "No" response → Polite call end message
- [ ] Test unclear response → Retry/clarification message
- [ ] End call properly

### ✅ **Voice Input Testing**
- [ ] Start recording successfully
- [ ] Stop recording and send audio
- [ ] Receive transcription of speech
- [ ] Get appropriate AI response
- [ ] Audio response plays correctly

### ✅ **Error Handling**
- [ ] Handle connection failures gracefully
- [ ] Display error messages clearly
- [ ] Recover from audio processing errors
- [ ] Manage session timeouts

## 📈 Performance Notes

### **Audio Processing**
- **UI Audio**: Converted from WebM to 16kHz PCM for Google STT
- **AI Audio**: TTS audio sent in original format (optimized)
- **Processing Time**: ~1-3 seconds for complete response cycle

### **Supported Browsers**
- Chrome (recommended)
- Firefox
- Safari
- Edge

### **Requirements**
- Modern browser with WebRTC support
- Microphone access permission
- Stable internet connection for Google Cloud APIs

## 🔗 Related Files

- **`voice_bot.py`**: Main voice bot application
- **`test_ui.html`**: Test UI interface
- **`test_ui_quick.py`**: Auto-start test script
- **`config.py`**: Configuration settings
- **`README_DIALER_INTEGRATION.md`**: Full dialer integration guide

## 📞 Support

### Monitoring Commands
```bash
# Real-time logs
tail -f logs/voice_bot.log

# Check connections
netstat -an | grep 8765

# Check voice bot process
ps aux | grep voice_bot.py
```

### Quick Reset
```bash
# Stop everything
pkill -f voice_bot.py

# Restart
./start_production.sh
```

---

**Status**: ✅ Ready for testing  
**Last Updated**: Production version  
**Browser Requirements**: Modern browser with WebRTC support 