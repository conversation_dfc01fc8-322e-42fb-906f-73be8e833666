#!/usr/bin/env python3
"""
Test script to verify audio processing works correctly
"""

import asyncio
import json
import base64
import websockets
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_audio_processing():
    """Test audio processing with the voice bot"""
    
    # Connect to voice bot
    uri = "ws://localhost:8765/ws"
    
    try:
        async with websockets.connect(uri) as websocket:
            logger.info("🔌 Connected to voice bot")
            
            # 1. Send start event
            start_event = {
                "event": "start",
                "sequence_number": 1,
                "stream_id": "test_call_123",
                "bytes": None,
                "start": {
                    "stream_sid": "test_call_123",
                    "user_name": "Test User",
                    "flow_name": "test_flow",
                    "call_sid": "test_call_123",
                    "account_sid": "test_account",
                    "voice_name": "test_voice"
                }
            }
            
            logger.info("📤 Sending start event...")
            await websocket.send(json.dumps(start_event))
            
            # 2. Wait for greeting response
            logger.info("⏳ Waiting for greeting response...")
            response = await websocket.recv()
            data = json.loads(response)
            logger.info(f"📥 Received greeting: {data.get('type')}")
            
            # 3. Send text response instead of audio (for testing)
            logger.info("📤 Sending text response: 'Yes, I am interested'...")
            text_message = {
                'type': 'text',
                'data': 'Yes, I am interested'
            }
            
            await websocket.send(json.dumps(text_message))
            
            # 4. Wait for response
            logger.info("⏳ Waiting for response to text...")
            response = await websocket.recv()
            data = json.loads(response)
            logger.info(f"📥 Received response: {data.get('type')}")
            
            if data.get('type') == 'audio':
                logger.info("✅ Response received!")
                logger.info(f"📝 Text: {data.get('text', 'No text')}")
                logger.info(f"🎯 Next action: {data.get('next_action', 'unknown')}")
            else:
                logger.warning(f"⚠️ Unexpected response type: {data.get('type')}")
            
            logger.info("✅ Audio processing test completed!")
            
    except Exception as e:
        logger.error(f"❌ Error in audio processing test: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    logger.info("🧪 Starting audio processing test...")
    asyncio.run(test_audio_processing()) 