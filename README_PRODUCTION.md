# Policybazaar Voice Bot - Production Deployment

## 🚀 Quick Start

```bash
# 1. Clone the repository
git clone <repository-url>
cd voice-bot1

# 2. Add Google Cloud credentials
# Place your matrixteam.json file in the project root

# 3. Start production server
./start_production.sh
```

## 📋 Production Requirements

### System Requirements
- **OS**: Ubuntu 18.04+ / CentOS 7+ / Debian 9+
- **Python**: 3.8 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **CPU**: 2 cores minimum, 4 cores recommended
- **Storage**: 10GB available space
- **Network**: Low latency to Google Cloud

### Dependencies
- Python 3.8+
- Google Cloud credentials (matrixteam.json)
- Internet connection for Google Cloud APIs

## 🔧 Configuration

### Essential Files
```
voice-bot1/
├── voice_bot.py              # Main application
├── config.py                 # Configuration
├── requirements.txt          # Python dependencies
├── matrixteam.json          # Google Cloud credentials
├── start_production.sh      # Production startup script
├── README_PRODUCTION.md     # This file
└── logs/                    # Log directory (auto-created)
```

### Environment Variables
```bash
export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/matrixteam.json"
export PYTHONPATH="$(pwd)"
```

## 🎯 Production Features

### ✅ Optimized for Production
- **Host**: `0.0.0.0` (accepts external connections)
- **Port**: `8765` (WebSocket server)
- **Logging**: File-based with rotation (10MB max, 5 backups)
- **Error Handling**: Comprehensive error catching and logging
- **Performance**: Optimized audio processing (no unnecessary conversions)
- **Security**: Connection limits and timeouts

### 📊 Monitoring
```bash
# Monitor logs
tail -f logs/voice_bot.log

# Check server status
curl -I http://YOUR_SERVER_IP:8765

# Check running processes
ps aux | grep voice_bot.py
```

## 🔌 Dialer Integration

### WebSocket Endpoint
```
ws://YOUR_SERVER_IP:8765/ws
```

### Audio Format
- **Sample Rate**: 8000 Hz (8kHz)
- **Channels**: 1 (Mono)
- **Bit Depth**: 16-bit
- **Encoding**: LINEAR16 PCM
- **Chunk Size**: 320 bytes

### Message Format
```json
{
  "type": "audio|signal",
  "data": "base64_encoded_data|signal_name"
}
```

## 🛠️ Deployment Options

### Option 1: Direct Deployment
```bash
# On production server
./start_production.sh
```

### Option 2: Systemd Service
```bash
# Create service file
sudo nano /etc/systemd/system/policybazaar-voice-bot.service
```

```ini
[Unit]
Description=Policybazaar Voice Bot
After=network.target

[Service]
Type=simple
User=voicebot
WorkingDirectory=/path/to/voice-bot1
ExecStart=/path/to/voice-bot1/start_production.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable policybazaar-voice-bot
sudo systemctl start policybazaar-voice-bot
```

### Option 3: Docker (Optional)
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8765

CMD ["python", "voice_bot.py"]
```

## 🔍 Troubleshooting

### Common Issues

**1. Port Already in Use**
```bash
# Check what's using port 8765
sudo netstat -tulpn | grep 8765

# Kill existing process
sudo pkill -f voice_bot.py
```

**2. Google Cloud Authentication**
```bash
# Verify credentials
python3 -c "from google.cloud import speech; print('✅ Credentials working')"
```

**3. Memory Issues**
```bash
# Monitor memory usage
htop
free -h

# Restart if needed
sudo systemctl restart policybazaar-voice-bot
```

### Log Analysis
```bash
# Check for errors
grep ERROR logs/voice_bot.log

# Check connection issues
grep "connection" logs/voice_bot.log

# Monitor real-time
tail -f logs/voice_bot.log | grep -E "(ERROR|WARNING|connection)"
```

## 📈 Performance Optimization

### Recommended Settings
- **Max Connections**: 200 (configurable in config.py)
- **Session Timeout**: 300 seconds
- **Audio Buffer**: 8192 bytes
- **Ping Interval**: 20 seconds

### Monitoring Commands
```bash
# Check active connections
netstat -an | grep 8765 | wc -l

# Monitor CPU/Memory
top -p $(pgrep -f voice_bot.py)

# Check disk usage
df -h
du -sh logs/
```

## 🔒 Security Considerations

### Firewall Configuration
```bash
# Allow WebSocket port
sudo ufw allow 8765

# Allow SSH (if needed)
sudo ufw allow ssh
```

### SSL/TLS (Recommended for Production)
```bash
# Install nginx for SSL termination
sudo apt install nginx

# Configure nginx proxy to WebSocket
# (Detailed configuration available on request)
```

## 📞 Support

### Emergency Contacts
- **Technical Issues**: Check logs first
- **Integration Problems**: Refer to README_DIALER_INTEGRATION.md
- **Performance Issues**: Monitor system resources

### Useful Commands
```bash
# Restart service
sudo systemctl restart policybazaar-voice-bot

# Check service status
sudo systemctl status policybazaar-voice-bot

# View recent logs
journalctl -u policybazaar-voice-bot -f

# Backup logs
tar -czf logs_backup_$(date +%Y%m%d).tar.gz logs/
```

---

**Production Status**: ✅ Ready for deployment  
**Last Updated**: $(date)  
**Version**: 1.0.0 