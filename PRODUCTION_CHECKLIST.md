# 🚀 Production Deployment Checklist

## ✅ Pre-Deployment Checklist

### Essential Files
- [x] `voice_bot.py` - Main application (44KB, 915 lines)
- [x] `config.py` - Production configuration (4.8KB, 133 lines)
- [x] `requirements.txt` - Python dependencies (275B, 13 lines)
- [x] `matrixteam.json` - Google Cloud credentials (2.3KB, 14 lines)
- [x] `start_production.sh` - Production startup script (2.2KB, 74 lines)
- [x] `README_PRODUCTION.md` - Production documentation (5.0KB, 255 lines)
- [x] `README_DIALER_INTEGRATION.md` - Integration guide (6.9KB, 270 lines)
- [x] `logs/` - Log directory (auto-created)

### Configuration Verification
- [x] **Host**: `0.0.0.0` (accepts external connections)
- [x] **Port**: `8765` (WebSocket server)
- [x] **Logging**: File-based with rotation
- [x] **Audio Processing**: Optimized (no unnecessary conversions)
- [x] **Error Handling**: Comprehensive logging
- [x] **Performance**: Connection limits and timeouts

### Security & Performance
- [x] **Max Connections**: 200 (configurable)
- [x] **Session Timeout**: 300 seconds
- [x] **Audio Buffer**: 8192 bytes
- [x] **Ping Interval**: 20 seconds
- [x] **Log Rotation**: 10MB max, 5 backups

## 🧪 Testing Checklist

### Local Testing
```bash
# 1. Test startup script
./start_production.sh

# 2. Verify server starts
curl -I http://localhost:8765

# 3. Check logs
tail -f logs/voice_bot.log

# 4. Test WebSocket connection
# (Use a WebSocket client to test ws://localhost:8765/ws)
```

### Integration Testing
- [ ] Test with dialer team's bot.py
- [ ] Verify audio format compatibility (8kHz, mono, 16-bit)
- [ ] Test conversation flow (greeting → response → action)
- [ ] Verify session management (timeout, end signals)

## 🚀 Deployment Steps

### 1. Server Preparation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python 3.8+
sudo apt install python3 python3-pip python3-venv

# Create user (optional)
sudo adduser voicebot
sudo usermod -aG sudo voicebot
```

### 2. Application Deployment
```bash
# Clone/copy application
cd /opt/
sudo mkdir voice-bot1
sudo chown voicebot:voicebot voice-bot1
cd voice-bot1

# Copy all production files
# (voice_bot.py, config.py, requirements.txt, etc.)

# Set permissions
chmod +x start_production.sh
```

### 3. Environment Setup
```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Set environment variables
export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/matrixteam.json"
export PYTHONPATH="$(pwd)"
```

### 4. Service Configuration (Optional)
```bash
# Create systemd service
sudo nano /etc/systemd/system/policybazaar-voice-bot.service

# Enable and start
sudo systemctl enable policybazaar-voice-bot
sudo systemctl start policybazaar-voice-bot
```

### 5. Firewall Configuration
```bash
# Allow WebSocket port
sudo ufw allow 8765

# Allow SSH (if needed)
sudo ufw allow ssh

# Enable firewall
sudo ufw enable
```

## 📊 Monitoring Setup

### Log Monitoring
```bash
# Real-time log monitoring
tail -f logs/voice_bot.log

# Error monitoring
grep ERROR logs/voice_bot.log

# Connection monitoring
grep "connection" logs/voice_bot.log
```

### System Monitoring
```bash
# Check process
ps aux | grep voice_bot.py

# Check connections
netstat -an | grep 8765

# Check memory/CPU
htop
```

## 🔧 Troubleshooting

### Common Issues & Solutions

**1. Port Already in Use**
```bash
sudo netstat -tulpn | grep 8765
sudo pkill -f voice_bot.py
```

**2. Google Cloud Authentication**
```bash
python3 -c "from google.cloud import speech; print('✅ Credentials working')"
```

**3. Memory Issues**
```bash
free -h
sudo systemctl restart policybazaar-voice-bot
```

**4. Network Issues**
```bash
# Test connectivity
ping googleapis.com
curl -I https://speech.googleapis.com
```

## 📞 Emergency Contacts

### Quick Commands
```bash
# Restart service
sudo systemctl restart policybazaar-voice-bot

# Check status
sudo systemctl status policybazaar-voice-bot

# View logs
journalctl -u policybazaar-voice-bot -f

# Kill process
sudo pkill -f voice_bot.py
```

### Support Resources
- **Logs**: `logs/voice_bot.log`
- **Integration Guide**: `README_DIALER_INTEGRATION.md`
- **Production Guide**: `README_PRODUCTION.md`

---

**Status**: ✅ Production Ready  
**Last Updated**: $(date)  
**Total Files**: 8 essential files  
**Size**: ~70KB (excluding logs) 